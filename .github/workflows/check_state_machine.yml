name: Check State Machine Diagram

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - '*'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        container:
        - 'px4io/px4-dev-simulation-bionic:2020-09-14'
    container: ${{ matrix.container }}
    steps:
    - uses: actions/checkout@v1
    - name: submodule update
      run: git submodule update --init --recursive
    - name: Install clang-format
      run: apt update && apt install -y clang-format-6.0
    - name: Check State Machine Diagram
      working-directory: tools
      run: |
        ./check_state_machine_diagrams.sh
