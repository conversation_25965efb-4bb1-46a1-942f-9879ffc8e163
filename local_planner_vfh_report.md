# Local Planner with Vector Field Histogram (VFH) Algorithm

## Table of Contents
1. [High-Level Overview](#high-level-overview)
2. [VFH Algorithm Steps](#vfh-algorithm-steps)
3. [Detailed Implementation](#detailed-implementation)
4. [Mathematical Foundations](#mathematical-foundations)
5. [Code Analysis](#code-analysis)
6. [Performance Considerations](#performance-considerations)

## High-Level Overview

The Local Planner is a sophisticated obstacle avoidance system that uses the Vector Field Histogram (VFH) algorithm combined with A* path planning to navigate a UAV through complex environments. The system processes 3D point cloud data from sensors to create a polar histogram representation of obstacles and generates safe navigation paths.

### Main Components:
1. **Point Cloud Processing**: Converts sensor data into usable obstacle information
2. **VFH Histogram Generation**: Creates polar histogram representation of obstacles
3. **Cost Matrix Calculation**: Evaluates navigation costs for different directions
4. **A* Tree Search**: Finds optimal path using star planner
5. **Path Execution**: Generates waypoints for vehicle control

### Key Features:
- Real-time obstacle avoidance
- 3D navigation capability
- Multi-sensor fusion support
- Dynamic reconfiguration
- Collision prevention integration

## VFH Algorithm Steps

### Step 1: Sensor Data Acquisition and Processing
The system receives point cloud data from multiple sensors and processes it for obstacle detection.

### Step 2: Polar Histogram Construction
Convert 3D point cloud data into a 2D polar histogram representation around the vehicle.

### Step 3: Obstacle Distance Calculation
Calculate distances to obstacles in each histogram bin.

### Step 4: Cost Matrix Generation
Evaluate navigation costs considering:
- Obstacle proximity
- Goal direction
- Vehicle dynamics
- Smoothness constraints

### Step 5: Candidate Direction Selection
Identify best navigation directions from the cost matrix.

### Step 6: Path Planning with A*
Use A* algorithm to build a tree of possible paths.

### Step 7: Path Execution
Generate waypoints and control commands for the vehicle.

## Detailed Implementation

### 1. Point Cloud Processing and Filtering

The local planner starts by processing incoming point cloud data from sensors:

```cpp
void LocalPlanner::runPlanner() {
  float elapsed_since_last_processing = 
    static_cast<float>((ros::Time::now() - last_pointcloud_process_time_).toSec());
  
  processPointcloud(final_cloud_, original_cloud_vector_, fov_fcu_frame_, 
                   yaw_fcu_frame_deg_, pitch_fcu_frame_deg_, position_, 
                   min_sensor_range_, max_sensor_range_, max_point_age_s_, 
                   elapsed_since_last_processing, min_num_points_per_cell_);
  
  last_pointcloud_process_time_ = ros::Time::now();
  determineStrategy();
}
```

**Key Parameters:**
- `min_sensor_range_`: Minimum detection distance (typically 0.2m)
- `max_sensor_range_`: Maximum detection distance (typically 15m)
- `max_point_age_s_`: Maximum age of point cloud data before discarding

### 2. VFH Histogram Generation

The core of the VFH algorithm lies in creating a polar histogram representation:

```cpp
void LocalPlanner::create2DObstacleRepresentation(const bool send_to_fcu) {
  Histogram new_histogram = Histogram(ALPHA_RES);
  to_fcu_histogram_.setZero();
  generateNewHistogram(new_histogram, final_cloud_, position_);
  
  if (send_to_fcu) {
    compressHistogramElevation(to_fcu_histogram_, new_histogram, position_);
    updateObstacleDistanceMsg(to_fcu_histogram_);
  }
  polar_histogram_ = new_histogram;
  generateHistogramImage(polar_histogram_);
}
```

#### Histogram Structure

The histogram is defined with specific resolution parameters:

```cpp
const int ALPHA_RES = 6;  // Angular resolution in degrees
const int GRID_LENGTH_Z = 360 / ALPHA_RES;  // Azimuth bins (60 bins)
const int GRID_LENGTH_E = 180 / ALPHA_RES;  // Elevation bins (30 bins)
```

**Mathematical Foundation:**
- **Azimuth Range**: [-180°, +180°] divided into 60 bins of 6° each
- **Elevation Range**: [-90°, +90°] divided into 30 bins of 6° each
- **Total Bins**: 60 × 30 = 1800 histogram cells

### 3. Coordinate System Transformations

#### Cartesian to Polar Histogram Conversion

```cpp
PolarPoint cartesianToPolarHistogram(float x, float y, float z, const Eigen::Vector3f& pos) {
  PolarPoint p_pol(0.0f, 0.0f, 0.0f);
  float den = (Eigen::Vector2f(x, y) - pos.topRows<2>()).norm();
  p_pol.e = std::atan2(z - pos.z(), den) * RAD_TO_DEG;          // Elevation [-90,+90]
  p_pol.z = std::atan2(x - pos.x(), y - pos.y()) * RAD_TO_DEG;  // Azimuth [-180,+180]
  p_pol.r = sqrt((x - pos.x())² + (y - pos.y())² + (z - pos.z())²);
  return p_pol;
}
```

**Mathematical Formulas:**
- **Distance**: $r = \sqrt{(x-x_0)^2 + (y-y_0)^2 + (z-z_0)^2}$
- **Elevation**: $e = \arctan2(z-z_0, \sqrt{(x-x_0)^2 + (y-y_0)^2}) \times \frac{180}{\pi}$
- **Azimuth**: $\alpha = \arctan2(x-x_0, y-y_0) \times \frac{180}{\pi}$

#### Polar to Histogram Index Conversion

```cpp
Eigen::Vector2i polarToHistogramIndex(const PolarPoint& p_pol, int res) {
  Eigen::Vector2i ev2(0, 0);
  PolarPoint p_wrapped = p_pol;
  wrapPolar(p_wrapped);
  
  // Elevation angle to y-axis histogram index
  ev2.y() = static_cast<int>(floor(p_wrapped.e / res + 90.0f / res));
  // Azimuth angle to x-axis histogram index  
  ev2.x() = static_cast<int>(floor(p_wrapped.z / res + 180.0f / res));
  
  // Clamp due to floating point errors
  if (ev2.x() >= 360 / res) ev2.x() = 360 / res - 1;
  if (ev2.x() < 0) ev2.x() = 0;
  if (ev2.y() >= 180 / res) ev2.y() = 180 / res - 1;
  if (ev2.y() < 0) ev2.y() = 0;
  
  return ev2;
}
```

**Index Calculation:**
- **Azimuth Index**: $i_z = \lfloor\frac{\alpha + 180°}{\text{ALPHA\_RES}}\rfloor$
- **Elevation Index**: $i_e = \lfloor\frac{e + 90°}{\text{ALPHA\_RES}}\rfloor$

### 4. Histogram Population

The histogram is populated by processing each point in the point cloud:

```cpp
void generateNewHistogram(Histogram& polar_histogram,
                         const pcl::PointCloud<pcl::PointXYZI>& cropped_cloud,
                         const Eigen::Vector3f& position) {
  Eigen::MatrixXi counter(GRID_LENGTH_E, GRID_LENGTH_Z);
  counter.fill(0);

  for (auto xyz : cropped_cloud) {
    Eigen::Vector3f p = toEigen(xyz);
    PolarPoint p_pol = cartesianToPolarHistogram(p, position);
    float dist = p_pol.r;
    Eigen::Vector2i p_ind = polarToHistogramIndex(p_pol, ALPHA_RES);

    counter(p_ind.y(), p_ind.x()) += 1;
    polar_histogram.set_dist(p_ind.y(), p_ind.x(),
                           polar_histogram.get_dist(p_ind.y(), p_ind.x()) + dist);
  }

  // Average the distances in each bin
  for (int e = 0; e < GRID_LENGTH_E; e++) {
    for (int z = 0; z < GRID_LENGTH_Z; z++) {
      if (counter(e, z) > 0) {
        polar_histogram.set_dist(e, z, polar_histogram.get_dist(e, z) / counter(e, z));
      }
    }
  }
}
```

**Algorithm Steps:**
1. Initialize distance accumulator and point counter matrices
2. For each point in the point cloud:
   - Convert to polar coordinates relative to vehicle position
   - Calculate histogram bin indices
   - Accumulate distance and increment counter
3. Average distances in each bin by dividing by point count

### 5. Cost Matrix Calculation

The cost matrix evaluates the desirability of moving in each direction:

```cpp
void getCostMatrix(const Histogram& histogram, const Eigen::Vector3f& goal,
                  const Eigen::Vector3f& position, const Eigen::Vector3f& velocity,
                  const costParameters& cost_params, float smoothing_margin_degrees,
                  const Eigen::Vector3f& closest_pt, const float max_sensor_range,
                  const float min_sensor_range, Eigen::MatrixXf& cost_matrix,
                  std::vector<uint8_t>& image_data) {

  cost_matrix.resize(GRID_LENGTH_E, GRID_LENGTH_Z);
  cost_matrix.fill(NAN);

  for (int e_index = 0; e_index < GRID_LENGTH_E; e_index += step_size) {
    for (int z_index = 0; z_index < GRID_LENGTH_Z; z_index += step_size) {
      float obstacle_distance = histogram.get_dist(e_index, z_index);
      PolarPoint p_pol = histogramIndexToPolar(e_index, z_index, ALPHA_RES, 1.0f);

      std::pair<float, float> costs = costFunction(p_pol, obstacle_distance, goal,
                                                  position, velocity, cost_params,
                                                  closest_pt, is_obstacle_facing_goal);
      cost_matrix(e_index, z_index) = costs.second;
    }
  }
}
```

#### Cost Function Components

The cost function combines multiple factors:

```cpp
std::pair<float, float> costFunction(const PolarPoint& candidate_polar, float obstacle_distance,
                                   const Eigen::Vector3f& goal, const Eigen::Vector3f& position,
                                   const Eigen::Vector3f& velocity, const costParameters& cost_params,
                                   const Eigen::Vector3f& closest_pt, const bool is_obstacle_facing_goal) {

  // 1. Goal direction cost
  const PolarPoint facing_goal = cartesianToPolarHistogram(goal, position);
  const float angle_diff = angleDifference(candidate_polar.z, facing_goal.z);
  const float yaw_cost = (1.f - weight) * cost_params.yaw_cost_param * angle_diff * angle_diff;

  // 2. Path line following cost
  const PolarPoint facing_line = cartesianToPolarHistogram(closest_pt, position);
  const float angle_diff_to_line = angleDifference(candidate_polar.z, facing_line.z);
  const float yaw_to_line_cost = weight * cost_params.yaw_cost_param * angle_diff_to_line * angle_diff_to_line;

  // 3. Velocity alignment cost
  const Eigen::Vector3f candidate_velocity_cartesian =
    polarHistogramToCartesian(candidate_polar, Eigen::Vector3f(0.0f, 0.0f, 0.0f));
  const float velocity_cost = cost_params.velocity_cost_param *
    (velocity.norm() - candidate_velocity_cartesian.normalized().dot(velocity));

  // 4. Pitch cost (altitude control)
  float pitch_cost = cost_params.pitch_cost_param *
    (candidate_polar.e - facing_goal.e) * (candidate_polar.e - facing_goal.e);

  // 5. Obstacle avoidance cost
  const float d = cost_params.obstacle_cost_param - obstacle_distance;
  const float distance_cost = obstacle_distance > 0 ? 5000.0f * (1 + d / sqrt(1 + d * d)) : 0.0f;

  return std::pair<float, float>(distance_cost,
                                velocity_cost + yaw_cost + yaw_to_line_cost + pitch_cost);
}
```

**Mathematical Formulation:**

**Total Cost**: $C_{total} = C_{velocity} + C_{yaw} + C_{line} + C_{pitch}$

Where:
- **Velocity Cost**: $C_{velocity} = k_v \cdot (|\vec{v}| - \hat{v}_{candidate} \cdot \vec{v})$
- **Yaw Cost**: $C_{yaw} = (1-w) \cdot k_{\psi} \cdot (\psi_{candidate} - \psi_{goal})^2$
- **Line Following Cost**: $C_{line} = w \cdot k_{\psi} \cdot (\psi_{candidate} - \psi_{line})^2$
- **Pitch Cost**: $C_{pitch} = k_{\theta} \cdot (\theta_{candidate} - \theta_{goal})^2$
- **Obstacle Cost**: $C_{obstacle} = 5000 \cdot (1 + \frac{d}{\sqrt{1 + d^2}})$ where $d = k_{obs} - d_{obstacle}$

### 6. A* Tree-Based Path Planning

The star planner uses A* algorithm to build a tree of possible paths:

```cpp
void StarPlanner::buildLookAheadTree() {
  tree_.clear();
  closed_set_.clear();

  // Insert root node at current position
  tree_.push_back(TreeNode(0, position_, velocity_));
  tree_.back().setCosts(treeHeuristicFunction(0), treeHeuristicFunction(0));

  int origin = 0;
  for (int n = 0; n < n_expanded_nodes_ && is_expanded_node; n++) {
    Eigen::Vector3f origin_position = tree_[origin].getPosition();
    Eigen::Vector3f origin_velocity = tree_[origin].getVelocity();

    // Generate histogram for current node position
    histogram.setZero();
    generateNewHistogram(histogram, cloud_, origin_position);

    // Calculate cost matrix and get best candidates
    getCostMatrix(histogram, goal_, origin_position, origin_velocity, cost_params_,
                 smoothing_margin_degrees_, closest_pt_, max_sensor_range_,
                 min_sensor_range_, cost_matrix, cost_image_data);
    getBestCandidatesFromCostMatrix(cost_matrix, children_per_node_, candidate_vector);

    // Add candidate nodes to tree
    for (candidateDirection candidate : candidate_vector) {
      PolarPoint candidate_polar = candidate.toPolar(tree_node_distance_);
      Eigen::Vector3f node_location = polarHistogramToCartesian(candidate_polar, origin_position);

      tree_.push_back(TreeNode(origin, node_location, node_velocity));
      float h = treeHeuristicFunction(tree_.size() - 1);
      tree_.back().total_cost_ = tree_[origin].total_cost_ - tree_[origin].heuristic_ + candidate.cost + h;
    }

    // Mark current node as closed and find next best node to expand
    closed_set_.push_back(origin);
    tree_[origin].closed_ = true;

    // Find node with minimum total cost for next expansion
    float minimal_cost = HUGE_VAL;
    is_expanded_node = false;
    for (size_t i = 0; i < tree_.size(); i++) {
      if (!(tree_[i].closed_)) {
        if (tree_[i].total_cost_ < minimal_cost) {
          minimal_cost = tree_[i].total_cost_;
          origin = i;
          is_expanded_node = true;
        }
      }
    }
  }
}
```

**A* Algorithm Components:**
- **g(n)**: Actual cost from start to node n
- **h(n)**: Heuristic cost from node n to goal
- **f(n)**: Total cost = g(n) + h(n)

**Heuristic Function:**
```cpp
float StarPlanner::treeHeuristicFunction(int node_number) const {
  return (goal_ - tree_[node_number].getPosition()).norm() * tree_heuristic_weight_;
}
```

$h(n) = k_h \cdot |\vec{p}_{goal} - \vec{p}_n|$

### 7. Path Extraction and Execution

After building the tree, the algorithm extracts the best path:

```cpp
// Find deepest node (furthest progress toward goal)
int max_depth = 0;
int max_depth_index = 0;
for (size_t i = 0; i < tree_.size(); i++) {
  if (!(tree_[i].closed_)) {
    if (tree_[i].depth_ > max_depth) {
      max_depth = tree_[i].depth_;
      max_depth_index = i;
    }
  }
}

// Build final path by backtracking through tree
int tree_end = max_depth_index;
path_node_positions_.clear();
while (tree_end > 0) {
  path_node_positions_.push_back(tree_[tree_end].getPosition());
  tree_end = tree_[tree_end].origin_;
}
path_node_positions_.push_back(tree_[0].getPosition());
```

## Mathematical Foundations

### Coordinate System Conventions

The VFH algorithm uses a specific coordinate system convention:

**Histogram Convention:**
- **Origin**: Vehicle position
- **Azimuth (z)**: Angle from positive Y-axis, clockwise positive [-180°, +180°]
- **Elevation (e)**: Angle from XY-plane, upward positive [-90°, +90°]
- **Radius (r)**: Distance from origin [0, ∞)

**Transformation Matrices:**

**Polar to Cartesian:**
$$\begin{bmatrix} x \\ y \\ z \end{bmatrix} = \begin{bmatrix} x_0 \\ y_0 \\ z_0 \end{bmatrix} + r \begin{bmatrix} \cos(e) \sin(\alpha) \\ \cos(e) \cos(\alpha) \\ \sin(e) \end{bmatrix}$$

**Cartesian to Polar:**
$$r = \sqrt{(x-x_0)^2 + (y-y_0)^2 + (z-z_0)^2}$$
$$e = \arctan2(z-z_0, \sqrt{(x-x_0)^2 + (y-y_0)^2})$$
$$\alpha = \arctan2(x-x_0, y-y_0)$$

### Histogram Discretization

The continuous polar space is discretized into bins:

**Bin Boundaries:**
- **Azimuth bin i**: $[\alpha_i, \alpha_{i+1}] = [i \cdot \Delta\alpha - 180°, (i+1) \cdot \Delta\alpha - 180°]$
- **Elevation bin j**: $[e_j, e_{j+1}] = [j \cdot \Delta e - 90°, (j+1) \cdot \Delta e - 90°]$

Where $\Delta\alpha = \Delta e = \text{ALPHA\_RES} = 6°$

### Cost Function Analysis

The multi-objective cost function balances several competing objectives:

1. **Goal Seeking**: Minimize angular deviation from goal direction
2. **Path Following**: Stay close to planned trajectory line
3. **Obstacle Avoidance**: Maintain safe distance from obstacles
4. **Smoothness**: Minimize velocity changes and maintain smooth motion
5. **Altitude Control**: Reach target altitude efficiently

**Weighted Sum Approach:**
$$C_{total} = w_1 C_{goal} + w_2 C_{path} + w_3 C_{obstacle} + w_4 C_{velocity} + w_5 C_{altitude}$$

The weights are dynamically adjusted based on:
- Distance to goal
- Obstacle density
- Mission phase (takeoff, cruise, landing)

## Code Analysis

### Key Data Structures

#### Histogram Class
```cpp
class Histogram {
  int resolution_;           // Angular resolution (ALPHA_RES = 6°)
  int z_dim_;               // Azimuth dimension (60 bins)
  int e_dim_;               // Elevation dimension (30 bins)
  Eigen::MatrixXf dist_;    // Distance matrix [30x60]

public:
  float get_dist(int x, int y) const;
  void set_dist(int x, int y, float value);
  void setZero();
  bool isEmpty() const;
};
```

#### TreeNode Structure
```cpp
class TreeNode {
  int origin_;                    // Parent node index
  Eigen::Vector3f position_;      // 3D position
  Eigen::Vector3f velocity_;      // 3D velocity
  float total_cost_;              // f(n) = g(n) + h(n)
  float heuristic_;               // h(n)
  int depth_;                     // Tree depth
  bool closed_;                   // A* closed set flag
};
```

### Algorithm Flow

```cpp
// Main planning cycle
void LocalPlanner::runPlanner() {
  // 1. Process point cloud data
  processPointcloud(final_cloud_, original_cloud_vector_, ...);

  // 2. Determine navigation strategy
  determineStrategy();
}

void LocalPlanner::determineStrategy() {
  // 3. Create 2D obstacle representation (VFH histogram)
  create2DObstacleRepresentation(px4_.param_cp_dist > 0.f);

  // 4. Calculate cost matrix
  getCostMatrix(polar_histogram_, goal_, position_, velocity_, ...);

  // 5. Build A* search tree
  star_planner_->buildLookAheadTree();
}
```

### Performance Optimizations

#### Sparse Processing
```cpp
// Only process non-empty histogram bins
for (int e_index = 0; e_index < GRID_LENGTH_E; e_index += step_size) {
  for (int z_index = 0; z_index < GRID_LENGTH_Z; z_index += step_size) {
    float obstacle_distance = histogram.get_dist(e_index, z_index);
    if (obstacle_distance > 0.01f) {  // Skip empty bins
      // Process cost calculation
    }
  }
}
```

#### Memory Management
```cpp
// Pre-allocate matrices to avoid dynamic allocation
cost_matrix.resize(GRID_LENGTH_E, GRID_LENGTH_Z);
cost_image_data.reserve(GRID_LENGTH_E * GRID_LENGTH_Z);
```

## Performance Considerations

### Computational Complexity

**Histogram Generation**: O(N) where N is the number of points in the point cloud
**Cost Matrix Calculation**: O(B) where B is the number of histogram bins (1800)
**A* Tree Search**: O(b^d) where b is branching factor and d is search depth

### Memory Usage

- **Histogram Storage**: 1800 × 4 bytes = 7.2 KB per histogram
- **Cost Matrix**: 1800 × 4 bytes = 7.2 KB
- **Tree Nodes**: Typically 50-200 nodes × 64 bytes = 3.2-12.8 KB

### Real-time Performance

The algorithm is designed for real-time operation with typical execution times:
- **Histogram Generation**: 1-3 ms
- **Cost Calculation**: 2-5 ms
- **Tree Search**: 5-15 ms
- **Total Cycle Time**: 10-25 ms (40-100 Hz operation)

### Optimization Strategies

1. **Sparse Histogram Processing**: Only process non-empty bins
2. **Multi-resolution Approach**: Use coarse resolution for distant planning
3. **Incremental Updates**: Update only changed regions
4. **Parallel Processing**: Vectorize cost calculations
5. **Memory Pooling**: Reuse allocated memory between cycles

### Parameter Tuning

Critical parameters affecting performance and behavior:

```cpp
// Histogram resolution (trade-off: accuracy vs. computation)
const int ALPHA_RES = 6;  // 6° resolution

// Tree search parameters
int children_per_node_ = 1;      // Branching factor
int n_expanded_nodes_ = 5;       // Search depth
float tree_node_distance_ = 1.0f; // Node spacing

// Cost function weights
float yaw_cost_param = 2.0f;
float pitch_cost_param = 1.0f;
float velocity_cost_param = 0.5f;
float obstacle_cost_param = 5.0f;
```

This comprehensive implementation provides robust obstacle avoidance capabilities while maintaining real-time performance suitable for UAV navigation in complex environments.
