<launch>
    <arg name="world_path" default="$(find avoidance)/sim/worlds/outdoor_village_3.world" />
    <arg name="gui" default="false"/>
    <arg name="ns" default="/"/>
    <arg name="model" default="iris_depth_camera"/>
    <arg name="fcu_url" default="udp://:14540@localhost:14557"/>
    <arg name="gcs_url" default="" />   <!-- GCS link is provided by SITL -->
    <arg name="tgt_system" default="1" />
    <arg name="tgt_component" default="1" />
    <arg name="vehicle" default="iris_obs_avoid"/>

    <param name="use_sim_time" value="true" />

    <!-- Launch rqt_reconfigure -->
    <node pkg="rqt_reconfigure" type="rqt_reconfigure" output="screen" name="rqt_reconfigure" />

    <!-- Launch PX4 SITL -->
    <include file="$(find px4)/launch/px4.launch">
        <arg name="vehicle" value="$(arg vehicle)"/>
    </include>

    <!-- Launch MavROS -->
    <group ns="$(arg ns)">
        <include file="$(find mavros)/launch/node.launch">
            <arg name="pluginlists_yaml" value="$(find mavros)/launch/px4_pluginlists.yaml" />
            <!-- Need to change the config file to get the tf topic and get local position in terms of local origin -->
            <arg name="config_yaml" value="$(find avoidance)/resource/px4_config.yaml" />
            <arg name="fcu_url" value="$(arg fcu_url)" />
            <arg name="gcs_url" value="$(arg gcs_url)" />
            <arg name="tgt_system" value="$(arg tgt_system)" />
            <arg name="tgt_component" value="$(arg tgt_component)" />
        </include>
    </group>

    <!-- Launch Gazebo -->
    <include file="$(find gazebo_ros)/launch/empty_world.launch">
        <arg name="gui" value="$(arg gui)"/>
        <arg name="world_name" value="$(arg world_path)" />
    </include>

    <!-- Spawn vehicle model -->
    <node name="spawn_model" pkg="gazebo_ros" type="spawn_model" output="screen"
          args="-sdf -database $(arg model) -model $(arg vehicle)">
    </node>

</launch>
