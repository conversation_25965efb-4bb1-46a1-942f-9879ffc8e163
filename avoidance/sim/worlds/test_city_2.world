<sdf version='1.4'>
  <world name='default'>
    <include>
      <uri>model://ground_plane</uri>
    </include>

    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>
    <physics name='default_physics' default='0' type='ode'>
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>10</iters>
          <sor>1.3</sor>
          <use_dynamic_moi_rescaling>0</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
      <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>
    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 10 0 0 0 0</pose>
    </model>
    <model name='asphalt_plane_0'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 -10 0 0 0 0</pose>
    </model>
    <model name='asphalt_plane_1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 10 0 0 0 0</pose>
    </model>
    <model name='asphalt_plane_2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 -10 0 0 0 0</pose>
    </model>
    <model name='asphalt_plane_3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 -30 0 0 0 0</pose>
    </model>
    <model name='asphalt_plane_4'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 -30 0 0 0 0</pose>
    </model>
    <model name='asphalt_plane_5'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 -30 0 0 0 0</pose>
    </model>
    <model name='asphalt_plane_6'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 10 0 0 0 0</pose>
    </model>
    <model name='asphalt_plane_7'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 -10 0 0 0 0</pose>
    </model>
    <model name='Fast Food'>
      <static>1</static>
      <link name='link'>
        <pose>0 0 1.57966 0 0 0</pose>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fast_food/meshes/fast_food.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fast_food/meshes/fast_food.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://fast_food/materials/scripts</uri>
              <uri>model://fast_food/materials/textures</uri>
              <name>FastFood/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>FastFood_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-5 -13 0 0 0 0</pose>
    </model>
    <model name='Dumpster'>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://dumpster/meshes/dumpster.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://dumpster/materials/scripts</uri>
              <uri>model://dumpster/materials/textures</uri>
              <name>Dumpster/Diffuse</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <inertial>
          <inertia>
            <ixx>1</ixx>
            <ixy>0</ixy>
            <ixz>0</ixz>
            <iyy>1</iyy>
            <iyz>0</iyz>
            <izz>1</izz>
          </inertia>
          <mass>1</mass>
        </inertial>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-8.5 -12 0 0 0 0</pose>
      <static>0</static>
    </model>
    <model name='drc_practice_yellow_parking_block'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-5 0 0 0 0 0</pose>
    </model>
    <model name='drc_practice_yellow_parking_block_0'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-5 -2.5 0 0 0 0</pose>
    </model>
    <model name='drc_practice_yellow_parking_block_1'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-5 2.5 0 0 0 0</pose>
    </model>
    <model name='drc_practice_yellow_parking_block_2'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-4 2.5 0 0 0 0</pose>
    </model>
    <model name='drc_practice_yellow_parking_block_3'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-4 0 0 0 0 0</pose>
    </model>
    <model name='drc_practice_yellow_parking_block_4'>
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_yellow_parking_block/meshes/block.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-4 -2.5 0 0 0 0</pose>
    </model>
    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>13 16 0 0 0 0</pose>
    </model>
    <model name='House 1b'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>13 0 0 0 0 0</pose>
    </model>
    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>0 15 0 0 0 0</pose>
    </model>
    <model name='House 2b'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>0 -5 0 0 0 0</pose>
    </model>
    <model name='House 3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-8 14 0 0 0 0</pose>
    </model>
    <model name='House 3b'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-8 0 0 0 0 1.57</pose>
    </model>
    <model name='House 3c'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-17 -7 0 0 0 1.57</pose>
    </model>
    <model name='House 3d'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-17 3 0 0 0 1.57</pose>
    </model>
    <model name='House 3e'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-17 13 0 0 0 1.57</pose>
    </model>
    <model name='Office Building 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose> 30 10 0 0 0 0</pose>
    </model>
    <model name='Office Building 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose> 19 -29 0 0 0 0</pose>
    </model>
    <model name='Office Building 3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose> -2 -29 0 0 0 0</pose>
    </model>
    <model name='Lamp Post'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://lamp_post/meshes/lamp_post.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://lamp_post/meshes/lamp_post.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose> -15 -5 0 0 0 0</pose>
    </model>
    <model name='mud_box'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual_1'>
          <pose>-2 2.5 0 0 0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_2'>
          <pose>2 2.5 0 0 0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_3'>
          <pose>2 -2.5 0 0 0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <visual name='visual_4'>
          <pose>-2 -2.5 0 0 0 0</pose>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>4 5 0.2</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://mud_box/materials/scripts</uri>
              <uri>model://mud_box/materials/textures</uri>
              <name>vrc/mud</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-16.1 -21 0 0 0 0</pose>
    </model>
    <model name='ladder'>
      <static>1</static>
      <link name='link'>
        <collision name='step0'>
          <pose>0 -1 0.3 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.1016 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='step1'>
          <pose>0 -0.79106 0.59054 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.1016 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='step2'>
          <pose>0 -0.61509 0.89508 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.1016 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='step3'>
          <pose>0 -0.43912 1.19962 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.1016 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='step4'>
          <pose>0 -0.26315 1.50416 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.1016 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='step5'>
          <pose>0 -0.08718 1.8087 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.1016 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='step6'>
          <pose>0 0.08879 2.11324 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.1016 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='step7'>
          <pose>0 0.26476 2.41778 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.1016 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='landing'>
          <pose>0 0.73039 2.72432 0 0 0</pose>
          <geometry>
            <box>
              <size>0.9 0.6096 0.0381</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_side_wall'>
          <pose>-0.43217 -0.37451 1.3386 1.0472 0 0</pose>
          <geometry>
            <box>
              <size>0.0508 3.16758 0.13</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_side_landing_wall'>
          <pose>-0.43217 0.69819 2.667 0 0 0</pose>
          <geometry>
            <box>
              <size>0.0508 0.67401 0.1524</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_landing_3'>
          <pose>-0.433355 0.66933 3.81 1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.73172</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_landing_2'>
          <pose>-0.433355 0.73166 3.5433 1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.5588</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_landing_1'>
          <pose>-0.433355 0.73166 3.2766 1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.5588</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_landing_0'>
          <pose>-0.433355 0.73166 3.0099 1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.5588</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_landing_upright_back'>
          <pose>-0.433355 1.01088 3.2766 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>1.0668</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_landing_upright_front'>
          <pose>-0.433355 0.45208 3.2766 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>1.0668</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_upright_4'>
          <pose>-0.433355 0.0699 2.80986 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_upright_3'>
          <pose>-0.433355 -0.31006 2.15169 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_upright_2'>
          <pose>-0.433355 -0.6911 1.49172 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_upright_1'>
          <pose>-0.433355 -1.0732 0.82995 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_upright_0'>
          <pose>-0.433355 -1.45316 0.17179 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='left_railing_long'>
          <pose>-0.433355 -0.781 1.9068 -0.523503 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>4.42295</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_side_wall'>
          <pose>0.43217 -0.37451 1.3386 1.0472 0 0</pose>
          <geometry>
            <box>
              <size>0.0508 3.16758 0.13</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_side_landing_wall'>
          <pose>0.43217 0.69819 2.667 0 0 0</pose>
          <geometry>
            <box>
              <size>0.0508 0.67401 0.1524</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_landing_3'>
          <pose>0.433355 0.66933 3.81 1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.73172</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_landing_2'>
          <pose>0.433355 0.73166 3.5433 1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.5588</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_landing_1'>
          <pose>0.433355 0.73166 3.2766 1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.5588</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_landing_0'>
          <pose>0.433355 0.73166 3.0099 1.5707 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.5588</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_landing_upright_back'>
          <pose>0.433355 1.01088 3.2766 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>1.0668</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_landing_upright_front'>
          <pose>0.433355 0.45208 3.2766 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>1.0668</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_upright_4'>
          <pose>0.433355 0.0699 2.80986 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_upright_3'>
          <pose>0.433355 -0.31006 2.15169 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_upright_2'>
          <pose>0.433355 -0.6911 1.49172 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_upright_1'>
          <pose>0.433355 -1.0732 0.82995 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_upright_0'>
          <pose>0.433355 -1.45316 0.17179 1.03414 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>0.57</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <collision name='right_railing_long'>
          <pose>0.433355 -0.781 1.9068 -0.523503 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.02413</radius>
              <length>4.42295</length>
            </cylinder>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://drc_practice_ladder/meshes/ladder.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-13 -13 0 0 0 0</pose>
    </model>
    <model name='grey_wall'>
      <static>1</static>
      <link name='link'>
        <pose>0 0 1.4 0 0 0</pose>
        <collision name='collision'>
          <geometry>
            <box>
              <size>7.5 0.2 2.8</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>7.5 0.2 2.8</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://grey_wall/materials/scripts</uri>
              <uri>model://grey_wall/materials/textures</uri>
              <name>vrc/grey_wall</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-16 -16 0 0 0 0</pose>
    </model>
    <model name='grey_wall_0'>
      <static>1</static>
      <link name='link'>
        <pose>0 0 1.4 0 0 0</pose>
        <collision name='collision'>
          <geometry>
            <box>
              <size>7.5 0.2 2.8</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>7.5 0.2 2.8</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://grey_wall/materials/scripts</uri>
              <uri>model://grey_wall/materials/textures</uri>
              <name>vrc/grey_wall</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-12.15 -19.7 0 0 0 0</pose>
    </model>
    <state world_name='default'>
      <sim_time>786 960000000</sim_time>
      <real_time>788 773741868</real_time>
      <wall_time>1402391362 370220840</wall_time>
      <model name='Dumpster'>
        <pose>-8.64436 -11.8944 0.050849 4e-06 -0.000102 -2.96857</pose>
        <link name='link'>
          <pose>-8.64436 -11.8944 0.050849 4e-06 -0.000102 -2.96857</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='Fast Food'>
        <pose>-5 -13 0 0 0 0</pose>
        <link name='link'>
          <pose>-5 -13 1.57966 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='House 1'>
        <pose>13 16 0 0 0 0</pose>
        <link name='link'>
          <pose>13 16 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='House 2'>
        <pose>0 15 0 0 0 0</pose>
        <link name='link'>
          <pose>0 15 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='House 3'>
        <pose>-8 14 0 0 0 0</pose>
        <link name='link'>
          <pose>-8 14 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane'>
        <pose>10 10 0 0 0 0</pose>
        <link name='link'>
          <pose>10 10 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane_0'>
        <pose>10 -10 0 0 0 0</pose>
        <link name='link'>
          <pose>10 -10 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane_1'>
        <pose>-10 10 0 0 0 0</pose>
        <link name='link'>
          <pose>-10 10 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane_2'>
        <pose>-10 -10 0 0 0 0</pose>
        <link name='link'>
          <pose>-10 -10 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane_3'>
        <pose>-10 -30 0 0 0 0</pose>
        <link name='link'>
          <pose>-10 -30 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane_4'>
        <pose>10 -30 0 0 0 0</pose>
        <link name='link'>
          <pose>10 -30 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane_5'>
        <pose>30 -30 0 0 0 0</pose>
        <link name='link'>
          <pose>30 -30 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane_6'>
        <pose>30 10 0 0 0 0</pose>
        <link name='link'>
          <pose>30 10 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='asphalt_plane_7'>
        <pose>30 -10 0 0 0 0</pose>
        <link name='link'>
          <pose>30 -10 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='drc_practice_yellow_parking_block'>
        <pose>-5 0 0 0 0 0</pose>
        <link name='link'>
          <pose>-5 0 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='drc_practice_yellow_parking_block_0'>
        <pose>-5 -2.5 0 0 0 0</pose>
        <link name='link'>
          <pose>-5 -2.5 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='drc_practice_yellow_parking_block_1'>
        <pose>-5 2.5 0 0 0 0</pose>
        <link name='link'>
          <pose>-5 2.5 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='drc_practice_yellow_parking_block_2'>
        <pose>-4 2.5 0 0 0 0</pose>
        <link name='link'>
          <pose>-4 2.5 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='drc_practice_yellow_parking_block_3'>
        <pose>-4 0 0 0 0 0</pose>
        <link name='link'>
          <pose>-4 0 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='drc_practice_yellow_parking_block_4'>
        <pose>-4 -2.5 0 0 0 0</pose>
        <link name='link'>
          <pose>-4 -2.5 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='grey_wall'>
        <pose>-16 -16 0 0 0 0</pose>
        <link name='link'>
          <pose>-16 -16 1.4 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='grey_wall_0'>
        <pose>-12.15 -19.7 0 0 0 -1.57378</pose>
        <link name='link'>
          <pose>-12.15 -19.7 1.4 0 0 -1.57378</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='ground_plane'>
        <pose>0 0 0 0 0 0</pose>
        <link name='link'>
          <pose>0 0 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='ladder'>
        <pose>-13.3 -15.3 0.1 0 0 -3.1415</pose>
        <link name='link'>
          <pose>-13.3 -15.3 0.1 0 0 -3.1415</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
      <model name='mud_box'>
        <pose>-16.1 -21 0 0 0 0</pose>
        <link name='link'>
          <pose>-16.1 -21 0 0 0 0</pose>
          <velocity>0 0 0 0 0 0</velocity>
          <acceleration>0 0 0 0 0 0</acceleration>
          <wrench>0 0 0 0 0 0</wrench>
        </link>
      </model>
    </state>
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>-15 0 4.57585 0 0.317796 0</pose>
        <view_controller>orbit</view_controller>
      </camera>
    </gui>
    <model name='Gas Station'>
      <static>1</static>
      <link name='link'>
        <pose>0 0 0 0 0 0</pose>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://gas_station/meshes/gas_station.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://gas_station/meshes/gas_station.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://gas_station/materials/scripts</uri>
              <uri>model://gas_station/materials/textures</uri>
              <name>GasStation/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>GasStation_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>33 -11 0 0 0 -1.5708</pose>
    </model>
  </world>
</sdf>
