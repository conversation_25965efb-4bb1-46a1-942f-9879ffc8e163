<sdf version='1.6'>
  <world name='default'>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose frame=''>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
              <torsional>
                <ode/>
              </torsional>
            </friction>
            <contact>
              <ode/>
            </contact>
            <bounce/>
          </surface>
          <max_contacts>10</max_contacts>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
      </link>
    </model>
    <gravity>0 0 -9.8</gravity>
    <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    <atmosphere type='adiabatic'/>
    <physics name='default_physics' default='0' type='ode'>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>



    <model name="left">
      <static>true</static>
      <link name='box'>
        <pose>5 -0.15 3.0 0 0 0</pose>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.05 0.05 1.8</size>
            </box>
          </geometry>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.05 0.05 1.8</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Black</name>
            </script>
          </material>
        </visual>
      </link>
    </model>

    <model name="bottom">
     <static>true</static>
     <link name='box'>
       <pose>5 0.45 2.1 0 0 1.57</pose>
       <collision name='collision'>
         <geometry>
           <box>
             <size>1.2 0.05 0.05</size>
           </box>
         </geometry>
       </collision>
       <visual name='visual'>
         <geometry>
           <box>
             <size>1.2 0.05 0.05</size>
           </box>
         </geometry>
         <material>
           <script>
             <uri>file://media/materials/scripts/gazebo.material</uri>
             <name>Gazebo/Black</name>
           </script>
         </material>
       </visual>
     </link>
   </model>

   <model name="right">
    <static>true</static>
    <link name='box'>
      <pose>5 1.05 3.0 0 0 0</pose>
      <collision name='collision'>
        <geometry>
          <box>
            <size>0.05 0.05 1.8</size>
          </box>
        </geometry>
      </collision>
      <visual name='visual'>
        <geometry>
          <box>
            <size>0.05 0.05 1.8</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Black</name>
          </script>
        </material>
      </visual>
    </link>
  </model>

  <model name="top">
   <static>true</static>
   <link name='box'>
     <pose>5 0.45 3.9 0 0 1.57</pose>
     <collision name='collision'>
       <geometry>
         <box>
           <size>1.2 0.05 0.05</size>
         </box>
       </geometry>
     </collision>
     <visual name='visual'>
       <geometry>
         <box>
           <size>1.2 0.05 0.05</size>
         </box>
       </geometry>
       <material>
         <script>
           <uri>file://media/materials/scripts/gazebo.material</uri>
           <name>Gazebo/Black</name>
         </script>
       </material>
     </visual>
   </link>
 </model>


 <model name="wall_right">
  <static>true</static>
  <link name='box'>
    <pose>5 -2.68 4.5 0 0 0</pose>
    <collision name='collision'>
      <geometry>
        <box>
          <size>0.05 5 9</size>
        </box>
      </geometry>
    </collision>
    <visual name='visual'>
      <geometry>
        <box>
          <size>0.05 5 9</size>
        </box>
      </geometry>
      <material>
        <script>
          <uri>file://media/materials/scripts/gazebo.material</uri>
          <name>Gazebo/Bricks</name>
        </script>
      </material>
    </visual>
  </link>
</model>

<model name="wall_left">
 <static>true</static>
 <link name='box'>
   <pose>5 3.57 4.5 0 0 0</pose>
   <collision name='collision'>
     <geometry>
       <box>
         <size>0.05 5 9</size>
       </box>
     </geometry>
   </collision>
   <visual name='visual'>
     <geometry>
       <box>
         <size>0.05 5 9</size>
       </box>
     </geometry>
     <material>
       <script>
         <uri>file://media/materials/scripts/gazebo.material</uri>
         <name>Gazebo/Bricks</name>
       </script>
     </material>
   </visual>
 </link>
</model>

<model name="wall_top">
 <static>true</static>
 <link name='box'>
   <pose>5 0.45 6.43 0 0 0</pose>
   <collision name='collision'>
     <geometry>
       <box>
         <size>0.05 1.3 5</size>
       </box>
     </geometry>
   </collision>
   <visual name='visual'>
     <geometry>
       <box>
         <size>0.05 1.3 5</size>
       </box>
     </geometry>
     <material>
       <script>
         <uri>file://media/materials/scripts/gazebo.material</uri>
         <name>Gazebo/Bricks</name>
       </script>
     </material>
   </visual>
 </link>
</model>

<model name="wall_bottom">
 <static>true</static>
 <link name='box'>
   <pose>5 0.45 1 0 0 0</pose>
   <collision name='collision'>
     <geometry>
       <box>
         <size>0.05 1.3 2.2</size>
       </box>
     </geometry>
   </collision>
   <visual name='visual'>
     <geometry>
       <box>
         <size>0.05 1.3 2.2</size>
       </box>
     </geometry>
     <material>
       <script>
         <uri>file://media/materials/scripts/gazebo.material</uri>
         <name>Gazebo/Bricks</name>
       </script>
     </material>
   </visual>
 </link>
</model>
  </world>
</sdf>
