<sdf version='1.4'>
  <world name='default'>
    <include>
      <uri>model://ground_plane</uri>
    </include>

    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>
    <physics name='default_physics' default='0' type='ode'>
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>10</iters>
          <sor>1.3</sor>
          <use_dynamic_moi_rescaling>0</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
      <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    </physics>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    <spherical_coordinates>
      <surface_model>EARTH_WGS84</surface_model>
      <latitude_deg>0</latitude_deg>
      <longitude_deg>0</longitude_deg>
      <elevation>0</elevation>
      <heading_deg>0</heading_deg>
    </spherical_coordinates>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-30 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>-10 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>30 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>50 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>70 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>90 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>110 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>130 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>150 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>170 190 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 -30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 -10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 10 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 30 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 50 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 70 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 90 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 110 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 130 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 150 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 170 0 0 0 0</pose>
    </model>

    <model name='asphalt_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <cast_shadows>0</cast_shadows>
          <geometry>
            <box>
              <size>20 20 0.1</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>model://asphalt_plane/materials/scripts</uri>
              <uri>model://asphalt_plane/materials/textures</uri>
              <name>vrc/asphalt</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>190 190 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>31 -40 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>20 -40 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 -40 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>31 -58 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>20 -59 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 -58 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>34 -50 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>8 -50 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>31 -6 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>20 -6 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 -6 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>31 -24 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>20 -25 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 -24 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>34 -16 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>8 -16 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>31 28 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>20 28 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 28 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>31 10 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>20 9 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 10 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>34 18 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>8 18 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>31 62 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>20 62 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 62 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>31 44 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>20 43 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 44 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>34 52 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>8 52 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>69 -40 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>58 -40 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>48 -40 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>69 -58 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>58 -59 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>48 -58 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>72 -50 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>46 -50 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>69 -6 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>58 -6 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>48 -6 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>69 -24 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>58 -25 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>48 -24 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>72 -16 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>46 -16 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>69 28 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>58 28 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>48 28 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>69 10 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>58 9 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>48 10 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>72 18 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>46 18 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>69 62 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>58 62 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>48 62 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>69 44 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>58 43 0 0 0 0</pose>
    </model>

    <model name='House 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_1/meshes/house_1.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_1/materials/scripts</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_1/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>48 44 0 0 0 0</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>72 52 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 2'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_2/meshes/house_2.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_2/materials/scripts</uri>
              <uri>model://house_2/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_2/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>46 52 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>44 4 0 0 0 1.57079632679</pose>
    </model>

    <model name='House 3'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://house_3/meshes/house_3.dae</uri>
            </mesh>
          </geometry>
          <material>
            <script>
              <uri>model://house_3/materials/scripts</uri>
              <uri>model://house_3/materials/textures</uri>
              <uri>model://house_1/materials/textures</uri>
              <name>House_3/Diffuse</name>
            </script>
            <shader type='normal_map_object_space'>
              <normal_map>model://house_1/materials/textures/House_1_Normal.png</normal_map>
            </shader>
          </material>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose>10 4 0 0 0 1.57079632679</pose>
    </model>

    <model name='Office Building 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose> 90 -11 0 0 0 0</pose>
    </model>

    <model name='Office Building 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose> 90 10 0 0 0 0</pose>
    </model>

    <model name='Office Building 1'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
          <max_contacts>10</max_contacts>
          <surface>
            <bounce/>
            <friction>
              <ode/>
            </friction>
            <contact>
              <ode/>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://office_building/meshes/office_building.dae</uri>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0</linear>
          <angular>0</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>
      </link>
      <pose> 90 30 0 0 0 0</pose>
    </model>

    <state world_name='default'>
      <sim_time>786 960000000</sim_time>
      <real_time>788 773741868</real_time>
      <wall_time>1402391362 370220840</wall_time>
    </state>

      </world>
</sdf>
