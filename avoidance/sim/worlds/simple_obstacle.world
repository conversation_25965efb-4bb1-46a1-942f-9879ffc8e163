<?xml version="1.0" ?>
<sdf version="1.4">
  <world name="vrc_task_1">
    <!-- place user camera -->
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>-15 0 4.57585 0 0.317796 0</pose>
        <view_controller>orbit</view_controller>
      </camera>
    </gui>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
 <physics name='default_physics' default='0' type='ode'>
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>10</iters>
          <sor>1.0</sor>
          <use_dynamic_moi_rescaling>0</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
      <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    </physics>

    <!-- Light Source -->
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>

    <include>
      <uri>model://vrc_driving_terrain</uri>
    </include>

    <!-- Trees -->
    <include>
      <name>pine_tree_1</name>
      <uri>model://pine_tree</uri>
      <pose>4 7 0   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_2</name>
      <uri>model://pine_tree</uri>
      <pose>6.1 7.6 0   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_3</name>
      <uri>model://pine_tree</uri>
      <pose>19.3 -14 0   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_4</name>
      <uri>model://pine_tree</uri>
      <pose>16 -11.5 0  0 0 0</pose>
    </include>

    <include>
      <name>oak_tree_1</name>
      <uri>model://oak_tree</uri>
      <pose>10 0 0  0 0 0</pose>
    </include>
    <include>
      <name>oak_tree_2</name>
      <uri>model://oak_tree</uri>
      <pose>10.25 -4.25 0  0 0 0</pose>
    </include>
    <include>
      <name>oak_tree_3</name>
      <uri>model://oak_tree</uri>
      <pose>20.2 14.4 0  0 0 0</pose>
    </include>
    <include>
      <name>oak_tree_4</name>
      <uri>model://oak_tree</uri>
      <pose>30 8.5 0  0 0 0</pose>
    </include>
    <include>
      <name>oak_tree_5</name>
      <uri>model://oak_tree</uri>
      <pose>136.2 -0.4 0  0 0 0</pose>
    </include>
    <include>
      <name>oak_tree_6</name>
      <uri>model://oak_tree</uri>
      <pose>38 -9.8 0  0 0 0</pose>
    </include>
    <include>
      <name>oak_tree_7</name>
      <uri>model://oak_tree</uri>
      <pose>38.6 -4.6 0 0 0 0</pose>
    </include>

    <include>
      <name>oak_tree_8</name>
      <uri>model://oak_tree</uri>
      <pose>16 30 1.6 0.15 0 0</pose>
    </include>


  </world>
</sdf>
