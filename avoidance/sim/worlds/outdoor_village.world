<?xml version="1.0" ?>
<sdf version="1.4">
  <world name="vrc_task_1">
    <!-- place user camera -->
    <gui fullscreen='0'>
      <camera name='user_camera'>
        <pose>-15 0 4.57585 0 0.317796 0</pose>
        <view_controller>orbit</view_controller>
      </camera>
    </gui>
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
 <physics name='default_physics' default='0' type='ode'>
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>10</iters>
          <sor>1.0</sor>
          <use_dynamic_moi_rescaling>0</use_dynamic_moi_rescaling>
        </solver>
        <constraints>
          <cfm>0</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>100</contact_max_correcting_vel>
          <contact_surface_layer>0.001</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
      <magnetic_field>6e-06 2.3e-05 -4.2e-05</magnetic_field>
    </physics>

    <!-- Light Source -->
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>

    <!--
    <light type="point" name="light_bulb">
      <cast_shadows>false</cast_shadows>
      <pose>-14 0 5 0 0 0</pose>
      <diffuse>0.3 0.3 0.3 1</diffuse>
      <specular>0.0 0.0 0.0 1</specular>
      <attenuation>
        <range>80</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
    </light>
    -->

    <!--
    <light type="spot" name="lamp_post_light">
      <cast_shadows>true</cast_shadows>
      <pose>-3.01 -1.7 3.01   0.22 0 0</pose>
      <diffuse>0.5 0.5 0.5 1</diffuse>
      <specular>0.0 0.0 0.0 1</specular>
      <attenuation>
        <range>20</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>0 0.3 -0.9</direction>
      <spot>
        <inner_angle>0.6</inner_angle>
        <outer_angle>1.0</outer_angle>
        <falloff>1.0</falloff>
      </spot>
    </light>
    -->


    <include>
      <uri>model://vrc_driving_terrain</uri>
    </include>

    <!--
    <include>
      <uri>model://grass_plane</uri>
    </include>


    <include>
      <uri>model://ground_plane_small</uri>
    </include>
    -->

    <include>
      <name>house_2</name>
      <uri>model://house_2</uri>
      <pose>4 -7 0   0 0 0</pose>
    </include>

    <include>
      <name>house_2_2</name>
      <uri>model://house_2</uri>
      <pose>-12 8 0   0 0 1.5707</pose>
    </include>

    <include>
      <name>house_1</name>
      <uri>model://house_1</uri>
      <pose>-10 -12 0   0 0 3.1415</pose>
    </include>

    <include>
      <name>mailbox_1</name>
      <uri>model://mailbox</uri>
      <pose>3 -2.4 0   0 0 0</pose>
    </include>

    <include>
      <name>mailbox_2</name>
      <uri>model://mailbox</uri>
      <pose>-11 -2.5 0   0 0 0</pose>
    </include>

    <include>
      <name>dumpster</name>
      <uri>model://dumpster</uri>
      <pose>-13 3 0   0 0 0</pose>
    </include>

    <include>
      <name>speed_limit_sign</name>
      <uri>model://speed_limit_sign</uri>
      <pose>6.5 2.4 0   0 0 1.5707</pose>
    </include>

    <include>
      <name>air_speed_limit</name>
      <uri>model://speed_limit_sign</uri>
      <pose>-20.5 69.4 1.6  0 0 2.3</pose>
    </include>

    <include>
      <name>construction_barrel_1</name>
      <uri>model://construction_barrel</uri>
      <pose>-19 14 0   0 0 0</pose>
    </include>

    <include>
      <name>lamp_post_1</name>
      <uri>model://lamp_post</uri>
      <pose>-11.5 2.6 0   0 0 0</pose>
    </include>

    <include>
      <name>lamp_post_2</name>
      <uri>model://lamp_post</uri>
      <pose>-6 -2.6 0   0 0 3.1415</pose>
    </include>

    <include>
      <name>lamp_post_3</name>
      <uri>model://lamp_post</uri>
      <pose>3 2.6 0   0 0 0</pose>
    </include>

    <include>
      <name>lamp_post_4</name>
      <uri>model://lamp_post</uri>
      <pose>9 -2.6 0   0 0 3.1415</pose>
    </include>

    <include>
      <name>lamp_post_5</name>
      <uri>model://lamp_post</uri>
      <pose>-22 5 0   0 0 1.5707</pose>
    </include>

    <include>
      <name>construction_cone_1</name>
      <uri>model://construction_cone</uri>
      <pose>1.5 1.5 0   0 0 0</pose>
    </include>
    <include>
      <name>construction_cone_2</name>
      <uri>model://construction_cone</uri>
      <pose>-1.5 1.5 0   0 0 0</pose>
    </include>
    <include>
      <name>construction_cone_3</name>
      <uri>model://construction_cone</uri>
      <pose>1.5 -1.5 0   0 0 0</pose>
    </include>
    <include>
      <name>construction_cone_4</name>
      <uri>model://construction_cone</uri>
      <pose>-1.5 -1.5 0   0 0 0</pose>
    </include>

    <!-- Trees -->
    <include>
      <name>pine_tree_1</name>
      <uri>model://pine_tree</uri>
      <pose>-4.5 8.7 0   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_2</name>
      <uri>model://pine_tree</uri>
      <pose>-3.5 -9.5 0   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_3</name>
      <uri>model://pine_tree</uri>
      <pose>-7 4 0   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_4</name>
      <uri>model://pine_tree</uri>
      <pose>28 -5 0.17   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_5</name>
      <uri>model://pine_tree</uri>
      <pose>19 13 1.58   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_6</name>
      <uri>model://pine_tree</uri>
      <pose>31 -2.4 0   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_7</name>
      <uri>model://pine_tree</uri>
      <pose>-22 -8 0   0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_8</name>
      <uri>model://pine_tree</uri>
      <pose>75.3 30 -1.44  0 0 0</pose>
    </include>
    <include>
      <name>pine_tree_9</name>
      <uri>model://pine_tree</uri>
      <pose>16 -11.5 0  0 0 0</pose>
    </include>

    <!-- Roads & Garage ways -->
    <road name="main_street">
      <width>4</width>
      <point>-19    14   0.01</point>
      <point>-19    3    0.01</point>
      <point>-18.77 1.85 0.01</point>
      <point>-18.12 0.88 0.01</point>
      <point>-17.15 0.23 0.01</point>
      <point>-16    0    0.01</point>

      <point>10 0 0.01</point>
      <point>11.15 -0.23 0.01</point>
      <point>12.12 -0.88 0.01</point>
      <point>12.77 -1.85 0.01</point>
      <point>13    -3    0.01</point>
      <point>13    -14   0.01</point>
    </road>

   <!-- <include>
      <name>recorder_camera</name>
      <uri>model://recorder_camera</uri>
      <pose>-1 -3.08 3.0  0 0 -1.89</pose>
    </include>
-->
    <road name="garage_way_1">
      <width>1.75</width>
      <point>1.75 -2 0.01</point>
      <point>1.75 -6 0.01</point>
      <material>
        <script>
          <uri>file://media/materials/scripts/gazebo.material</uri>
          <name>Gazebo/Pedestrian</name>
        </script>
      </material>
    </road>

    <road name="garage_way_2">
      <width>2.5</width>
      <point>-12.5 -2 0.01</point>
      <point>-12.5 -8 0.01</point>
      <material>
        <script>
          <uri>file://media/materials/scripts/gazebo.material</uri>
          <name>Gazebo/Pedestrian</name>
        </script>
      </material>
    </road>
  </world>
</sdf>
