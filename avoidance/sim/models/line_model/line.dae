<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.76.0 commit date:2015-11-03, commit time:10:56, hash:f337fea</authoring_tool>
    </contributor>
    <created>2016-11-10T14:22:41</created>
    <modified>2016-11-10T14:22:41</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_cameras>
    <camera id="Camera-camera" name="Camera">
      <optics>
        <technique_common>
          <perspective>
            <xfov sid="xfov">49.13434</xfov>
            <aspect_ratio>1.333333</aspect_ratio>
            <znear sid="znear">0.1</znear>
            <zfar sid="zfar">100</zfar>
          </perspective>
        </technique_common>
      </optics>
      <extra>
        <technique profile="blender">
          <YF_dofdist>0</YF_dofdist>
          <shiftx>0</shiftx>
          <shifty>0</shifty>
        </technique>
      </extra>
    </camera>
  </library_cameras>
  <library_images/>
  <library_effects>
    <effect id="Metal_001-effect">
      <profile_COMMON>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="Metal_001-material" name="Metal_001">
      <instance_effect url="#Metal_001-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Cylinder_003-mesh" name="Cylinder.003">
      <mesh>
        <source id="Cylinder_003-mesh-positions">
          <float_array id="Cylinder_003-mesh-positions-array" count="192">0 1 -1 0 1 1 0.1950903 0.9807853 -1 0.1950903 0.9807853 1 0.3826835 0.9238795 -1 0.3826835 0.9238795 1 0.5555703 0.8314696 -1 0.5555703 0.8314696 1 0.7071068 0.7071068 -1 0.7071068 0.7071068 1 0.8314697 0.5555702 -1 0.8314697 0.5555702 1 0.9238795 0.3826834 -1 0.9238795 0.3826834 1 0.9807853 0.1950903 -1 0.9807853 0.1950903 1 1 0 -1 1 0 1 0.9807853 -0.1950902 -1 0.9807853 -0.1950902 1 0.9238796 -0.3826833 -1 0.9238796 -0.3826833 1 0.8314697 -0.5555702 -1 0.8314697 -0.5555702 1 0.7071068 -0.7071068 -1 0.7071068 -0.7071068 1 0.5555702 -0.8314697 -1 0.5555702 -0.8314697 1 0.3826833 -0.9238796 -1 0.3826833 -0.9238796 1 0.1950901 -0.9807853 -1 0.1950901 -0.9807853 1 -3.25841e-7 -1 -1 -3.25841e-7 -1 1 -0.1950907 -0.9807852 -1 -0.1950907 -0.9807852 1 -0.3826839 -0.9238793 -1 -0.3826839 -0.9238793 1 -0.5555707 -0.8314693 -1 -0.5555707 -0.8314693 1 -0.7071073 -0.7071064 -1 -0.7071073 -0.7071064 1 -0.83147 -0.5555697 -1 -0.83147 -0.5555697 1 -0.9238799 -0.3826827 -1 -0.9238799 -0.3826827 1 -0.9807854 -0.1950894 -1 -0.9807854 -0.1950894 1 -1 9.65599e-7 -1 -1 9.65599e-7 1 -0.9807851 0.1950913 -1 -0.9807851 0.1950913 1 -0.9238791 0.3826845 -1 -0.9238791 0.3826845 1 -0.8314689 0.5555713 -1 -0.8314689 0.5555713 1 -0.7071059 0.7071077 -1 -0.7071059 0.7071077 1 -0.5555691 0.8314704 -1 -0.5555691 0.8314704 1 -0.3826821 0.9238801 -1 -0.3826821 0.9238801 1 -0.1950888 0.9807856 -1 -0.1950888 0.9807856 1</float_array>
          <technique_common>
            <accessor source="#Cylinder_003-mesh-positions-array" count="64" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cylinder_003-mesh-normals">
          <float_array id="Cylinder_003-mesh-normals-array" count="183">0.09801727 0.9951847 0 0.2902847 0.9569404 0 0.4713967 0.8819213 0 0.6343932 0.7730106 0 0.7730103 0.6343936 0 0.8819215 0.4713966 0 0.9569404 0.2902848 0 0.9951847 0.09801727 0 0.9951847 -0.09801727 0 0.9569404 -0.2902848 0 0.8819216 -0.4713962 0 0.7730106 -0.6343932 0 0.6343934 -0.7730104 0 0.4713968 -0.8819214 0 0.2902843 -0.9569405 0 0.09801691 -0.9951848 0 -0.09801757 -0.9951848 0 -0.2902852 -0.9569402 0 -0.4713971 -0.8819211 0 -0.6343936 -0.7730103 0 -0.7730111 -0.6343926 0 -0.8819217 -0.4713961 0 -0.9569405 -0.2902839 0 -0.9951848 -0.09801661 0 -0.9951846 0.09801846 0 -0.95694 0.2902858 0 -0.881921 0.4713975 0 -0.7730096 0.6343944 0 -0.6343923 0.7730113 0 -0.4713955 0.881922 0 0 0 1 -0.0980165 0.9951848 0 -0.2902831 0.9569409 0 0 0 -1 0.2902846 0.9569405 0 0.6343935 0.7730103 0 0.7730106 0.6343932 0 0.9951848 -0.09801667 0 0.8819214 -0.4713968 0 0.7730103 -0.6343936 0 0.2902842 -0.9569405 0 -0.2902852 -0.9569402 0 -0.6343939 -0.77301 0 -0.7730109 -0.6343929 0 -0.9569407 -0.2902834 0 -0.9951849 -0.09801602 0 -0.9951847 0.09801787 0 -0.8819208 0.4713979 0 -0.6343926 0.7730111 0 0 0 1 -3.97511e-6 0 1 3.97512e-6 0 1 -1.36853e-6 0 1 1.43703e-6 0 1 -2.87796e-7 0 1 -3.88858e-7 0 1 -0.290283 0.9569408 0 -3.97511e-6 0 -1 3.97512e-6 0 -1 3.88857e-7 0 -1 0 0 -1</float_array>
          <technique_common>
            <accessor source="#Cylinder_003-mesh-normals-array" count="61" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cylinder_003-mesh-vertices">
          <input semantic="POSITION" source="#Cylinder_003-mesh-positions"/>
        </vertices>
        <polylist material="Metal_001-material" count="124">
          <input semantic="VERTEX" source="#Cylinder_003-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cylinder_003-mesh-normals" offset="1"/>
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>1 0 3 0 2 0 3 1 5 1 4 1 5 2 7 2 6 2 7 3 9 3 8 3 9 4 11 4 10 4 11 5 13 5 12 5 13 6 15 6 14 6 15 7 17 7 16 7 17 8 19 8 18 8 19 9 21 9 20 9 21 10 23 10 22 10 23 11 25 11 24 11 25 12 27 12 26 12 27 13 29 13 28 13 29 14 31 14 30 14 31 15 33 15 32 15 33 16 35 16 34 16 35 17 37 17 36 17 37 18 39 18 38 18 39 19 41 19 40 19 41 20 43 20 42 20 43 21 45 21 44 21 45 22 47 22 46 22 47 23 49 23 48 23 49 24 51 24 50 24 51 25 53 25 52 25 53 26 55 26 54 26 55 27 57 27 56 27 57 28 59 28 58 28 59 29 61 29 60 29 37 30 21 30 53 30 63 31 1 31 0 31 61 32 63 32 62 32 30 33 46 33 14 33 0 0 1 0 2 0 2 34 3 34 4 34 4 2 5 2 6 2 6 35 7 35 8 35 8 36 9 36 10 36 10 5 11 5 12 5 12 6 13 6 14 6 14 7 15 7 16 7 16 37 17 37 18 37 18 9 19 9 20 9 20 38 21 38 22 38 22 39 23 39 24 39 24 12 25 12 26 12 26 13 27 13 28 13 28 40 29 40 30 40 30 15 31 15 32 15 32 16 33 16 34 16 34 41 35 41 36 41 36 18 37 18 38 18 38 42 39 42 40 42 40 43 41 43 42 43 42 21 43 21 44 21 44 44 45 44 46 44 46 45 47 45 48 45 48 46 49 46 50 46 50 25 51 25 52 25 52 47 53 47 54 47 54 27 55 27 56 27 56 48 57 48 58 48 58 29 59 29 60 29 5 49 3 49 1 49 1 49 63 49 5 49 61 49 59 49 57 49 57 49 55 49 53 49 53 50 51 50 49 50 49 49 47 49 53 49 45 49 43 49 37 49 41 49 39 49 37 49 37 49 35 49 33 49 33 49 31 49 29 49 29 49 27 49 25 49 25 49 23 49 21 49 21 51 19 51 17 51 17 49 15 49 21 49 13 49 11 49 9 49 9 49 7 49 5 49 5 49 63 49 61 49 61 49 57 49 5 49 53 49 47 49 45 49 43 49 41 49 37 49 37 49 33 49 21 49 29 49 25 49 21 49 21 52 15 52 13 52 13 53 9 53 21 53 5 49 57 49 53 49 53 54 45 54 37 54 33 49 29 49 21 49 21 55 9 55 5 55 5 49 53 49 21 49 62 31 63 31 0 31 60 56 61 56 62 56 62 33 0 33 2 33 2 33 4 33 6 33 6 33 8 33 10 33 10 33 12 33 6 33 14 33 16 33 18 33 18 33 20 33 14 33 22 33 24 33 30 33 26 33 28 33 30 33 30 33 32 33 34 33 34 33 36 33 38 33 38 33 40 33 42 33 42 33 44 33 46 33 46 57 48 57 50 57 50 58 52 58 54 58 54 33 56 33 62 33 58 33 60 33 62 33 62 33 2 33 14 33 6 33 12 33 14 33 14 33 20 33 22 33 24 33 26 33 30 33 30 33 34 33 46 33 38 33 42 33 46 33 46 59 50 59 62 59 56 33 58 33 62 33 2 33 6 33 14 33 14 33 22 33 30 33 34 33 38 33 46 33 50 33 54 33 62 33 62 60 14 60 46 60</p>
        </polylist>
      </mesh>
    </geometry>
    <geometry id="Cylinder_022-mesh" name="Cylinder.022">
      <mesh>
        <source id="Cylinder_022-mesh-positions">
          <float_array id="Cylinder_022-mesh-positions-array" count="192">1940.01 -896.2041 -32.25238 1939.164 -921.4175 -30.69624 1939.981 -896.0525 -32.24632 1939.134 -921.2654 -30.69017 1939.992 -895.8976 -32.24007 1939.146 -921.1109 -30.68391 1940.044 -895.7462 -32.23386 1939.198 -920.9594 -30.67772 1940.135 -895.6036 -32.22797 1939.289 -920.8169 -30.67182 1940.261 -895.4755 -32.22259 1939.415 -920.6889 -30.66644 1940.417 -895.3668 -32.21794 1939.571 -920.5801 -30.66179 1940.597 -895.2819 -32.21419 1939.751 -920.495 -30.65805 1940.795 -895.2236 -32.21152 1939.949 -920.4368 -30.65536 1941.002 -895.1943 -32.20998 1940.156 -920.4076 -30.65382 1941.211 -895.1953 -32.20965 1940.365 -920.4086 -30.6535 1941.414 -895.2265 -32.21056 1940.567 -920.4398 -30.65441 1941.602 -895.2868 -32.21266 1940.756 -920.4999 -30.65651 1941.77 -895.3734 -32.21586 1940.923 -920.5866 -30.65972 1941.909 -895.4834 -32.22006 1941.063 -920.6968 -30.66391 1942.015 -895.6128 -32.22509 1941.169 -920.8258 -30.66893 1942.085 -895.7559 -32.23073 1941.238 -920.9691 -30.67459 1942.114 -895.9078 -32.23681 1941.268 -921.121 -30.68066 1942.102 -896.0624 -32.24306 1941.256 -921.2756 -30.68692 1942.05 -896.2138 -32.24926 1941.204 -921.4271 -30.69311 1941.959 -896.3563 -32.25516 1941.113 -921.5696 -30.69902 1941.833 -896.4845 -32.26054 1940.987 -921.6976 -30.7044 1941.677 -896.5932 -32.26519 1940.831 -921.8063 -30.70904 1941.497 -896.6781 -32.26893 1940.651 -921.8915 -30.71278 1941.3 -896.7365 -32.27162 1940.453 -921.9495 -30.71547 1941.092 -896.7655 -32.27315 1940.246 -921.9789 -30.71701 1940.883 -896.7647 -32.27347 1940.037 -921.9778 -30.71733 1940.681 -896.7335 -32.27257 1939.834 -921.9467 -30.71642 1940.492 -896.6732 -32.27047 1939.646 -921.8865 -30.71432 1940.325 -896.5866 -32.26726 1939.479 -921.7999 -30.71111 1940.185 -896.4766 -32.26307 1939.339 -921.6897 -30.70692 1940.079 -896.3475 -32.25804 1939.233 -921.5606 -30.70189</float_array>
          <technique_common>
            <accessor source="#Cylinder_022-mesh-positions-array" count="64" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cylinder_022-mesh-normals">
          <float_array id="Cylinder_022-mesh-normals-array" count="372">-0.4094956 -0.04260045 -0.9113169 -0.9227128 0.05396229 0.3816927 -0.3122485 0.06906282 0.9474868 -0.1679756 0.06637871 0.9835538 -0.1085298 0.06493985 0.9919698 -0.07420879 0.06399244 0.9951875 -0.05056643 0.06318986 0.9967197 -0.03197455 0.06257241 0.9975281 -0.01614886 0.06210887 0.9979388 -0.001235902 0.0616427 0.9980975 0.01390141 0.06110942 0.9980344 0.03036475 0.0604763 0.9977077 0.05015635 0.05991524 0.9969426 0.07647651 0.05872964 0.9953402 0.1161897 0.05720639 0.9915782 0.1923115 0.05391907 0.9798516 0.4092188 0.04258954 0.9114419 0.9227743 -0.05429744 -0.3814964 0.3124116 -0.06886279 -0.9474476 0.1680194 -0.06641077 -0.9835442 0.1085264 -0.06502091 -0.9919649 0.07415932 -0.06400787 -0.9951902 0.05058544 -0.06309586 -0.9967247 0.03193628 -0.06258922 -0.9975283 0.01624995 -0.06218606 -0.9979323 0.001225233 -0.06158804 -0.9981009 -0.01384013 -0.06110984 -0.9980351 -0.03040546 -0.06053936 -0.9977026 -0.05010992 -0.05982577 -0.9969503 -0.07652378 -0.05882662 -0.9953309 -0.001676559 -0.04028105 0.9991871 -0.1921186 -0.05385476 -0.979893 -0.1163328 -0.05715304 -0.9915645 0.001763403 0.04023528 -0.9991887 -0.4089438 -0.0421009 -0.9115879 -0.9231982 0.05427247 0.380473 -0.3130077 0.06940561 0.9472113 -0.1679863 0.06619822 0.9835642 -0.108354 0.06473612 0.9920024 -0.07415759 0.0638799 0.9951985 -0.05053561 0.0631355 0.9967247 -0.03200668 0.06272912 0.9975172 -0.01620709 0.06220531 0.9979318 -0.001271545 0.06158941 0.9981008 0.01386976 0.06116926 0.9980311 0.0304979 0.06048834 0.997703 0.05010008 0.05980235 0.9969522 0.07634443 0.05894905 0.9953375 0.116598 0.05743145 0.9915173 0.1914191 0.05427056 0.9800069 0.4093255 0.04258728 0.911394 0.9201725 -0.05528932 -0.3875895 0.3128775 -0.06863999 -0.94731 0.1679838 -0.06641864 -0.9835497 0.1084212 -0.06479984 -0.9919909 0.07421892 -0.06380611 -0.9951987 0.05041223 -0.06326669 -0.9967227 0.0320807 -0.062729 -0.9975149 0.01608973 -0.06211066 -0.9979396 0.001272261 -0.06171858 -0.9980928 -0.01384639 -0.06117069 -0.9980313 -0.03047841 -0.06055116 -0.9976997 -0.05015522 -0.0598005 -0.9969496 -0.07628005 -0.05895131 -0.9953423 -0.002265393 -0.04017299 0.9991903 -0.001530587 -0.04000055 0.9991985 -0.001738786 -0.0401566 0.9991919 -0.001763761 -0.04031509 0.9991855 -0.001752436 -0.0403065 0.9991859 -0.001723051 -0.04031985 0.9991854 -0.001726448 -0.04028397 0.9991869 -0.001720607 -0.04023575 0.9991887 -0.0016613 -0.03987145 0.9992035 -0.001839697 -0.04047405 0.999179 -0.001772046 -0.04029822 0.9991862 -0.001732289 -0.0402522 0.9991881 -0.001704752 -0.04025161 0.9991881 -0.001723051 -0.04027074 0.9991874 -0.001654505 -0.04028397 0.9991869 -0.001764714 -0.04032385 0.9991852 -0.001665592 -0.04039204 0.9991825 -0.001648962 -0.04025161 0.9991883 -0.00172013 -0.0403077 0.9991859 -0.001701354 -0.04042094 0.9991813 -0.001664936 -0.04016745 0.9991917 -0.001733481 -0.04025161 0.9991881 -0.001742422 -0.04023885 0.9991886 -0.001778721 -0.04031795 0.9991854 -0.001727581 -0.04031038 0.9991858 -0.001745998 -0.04024249 0.9991885 -0.001727581 -0.04018259 0.9991909 -0.001691281 -0.04029715 0.9991863 -0.001752197 -0.04026246 0.9991877 -0.1920539 -0.05434155 -0.9798788 -0.1164003 -0.05726736 -0.9915501 0.00163114 0.04016745 -0.9991917 0.001810073 0.04042577 -0.999181 0.00160247 0.04031866 -0.9991856 0.001672744 0.04025161 -0.9991883 0.00167483 0.04019618 -0.9991904 0.001704514 0.04029321 -0.9991865 0.001730561 0.04022997 -0.999189 0.00176382 0.04014378 -0.9991924 0.001829445 0.04024803 -0.9991881 0.001806795 0.04035282 -0.999184 0.001844227 0.04031765 -0.9991852 0.001688838 0.04034817 -0.9991843 0.001773059 0.04028856 -0.9991865 0.001729428 0.04032182 -0.9991853 0.001793205 0.04025286 -0.999188 0.00168538 0.04026901 -0.9991875 0.001927077 0.0402612 -0.9991874 0.001631736 0.04030966 -0.9991859 0.001741528 0.04031109 -0.9991858 0.001767337 0.04033344 -0.9991848 0.001719474 0.04038202 -0.9991829 0.001631796 0.04022157 -0.9991896 0.001741707 0.04026842 -0.9991875 0.001703143 0.04030805 -0.9991859 0.001782238 0.04023432 -0.9991888 0.001672267 0.04029142 -0.9991866 0.001716911 0.0403378 -0.9991847 0.001698374 0.04034364 -0.9991845 0.00176239 0.04028397 -0.9991868</float_array>
          <technique_common>
            <accessor source="#Cylinder_022-mesh-normals-array" count="124" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cylinder_022-mesh-vertices">
          <input semantic="POSITION" source="#Cylinder_022-mesh-positions"/>
        </vertices>
        <polylist material="Metal_001-material" count="124">
          <input semantic="VERTEX" source="#Cylinder_022-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cylinder_022-mesh-normals" offset="1"/>
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>0 0 1 0 3 0 2 1 3 1 5 1 4 2 5 2 7 2 6 3 7 3 9 3 8 4 9 4 11 4 10 5 11 5 13 5 12 6 13 6 15 6 14 7 15 7 17 7 16 8 17 8 19 8 18 9 19 9 21 9 21 10 23 10 22 10 23 11 25 11 24 11 25 12 27 12 26 12 27 13 29 13 28 13 29 14 31 14 30 14 31 15 33 15 32 15 33 16 35 16 34 16 35 17 37 17 36 17 37 18 39 18 38 18 39 19 41 19 40 19 41 20 43 20 42 20 43 21 45 21 44 21 45 22 47 22 46 22 47 23 49 23 48 23 49 24 51 24 50 24 51 25 53 25 52 25 52 26 53 26 55 26 54 27 55 27 57 27 56 28 57 28 59 28 58 29 59 29 61 29 23 30 47 30 45 30 62 31 63 31 1 31 60 32 61 32 63 32 44 33 24 33 42 33 2 34 0 34 3 34 4 35 2 35 5 35 6 36 4 36 7 36 8 37 6 37 9 37 10 38 8 38 11 38 12 39 10 39 13 39 14 40 12 40 15 40 16 41 14 41 17 41 18 42 16 42 19 42 20 43 18 43 21 43 20 44 21 44 22 44 22 45 23 45 24 45 24 46 25 46 26 46 26 47 27 47 28 47 28 48 29 48 30 48 30 49 31 49 32 49 32 50 33 50 34 50 34 51 35 51 36 51 36 52 37 52 38 52 38 53 39 53 40 53 40 54 41 54 42 54 42 55 43 55 44 55 44 56 45 56 46 56 46 57 47 57 48 57 48 58 49 58 50 58 50 59 51 59 52 59 54 60 52 60 55 60 56 61 54 61 57 61 58 62 56 62 59 62 60 63 58 63 61 63 5 64 3 64 1 64 1 65 63 65 5 65 61 66 59 66 9 66 57 67 55 67 13 67 53 68 51 68 17 68 49 69 47 69 21 69 45 70 43 70 25 70 41 71 39 71 29 71 37 72 35 72 33 72 33 73 31 73 37 73 29 74 27 74 41 74 25 75 23 75 45 75 21 76 19 76 49 76 17 77 15 77 53 77 13 78 11 78 57 78 9 79 7 79 61 79 5 80 63 80 7 80 59 81 57 81 11 81 51 82 49 82 19 82 43 83 41 83 27 83 37 84 31 84 39 84 27 85 25 85 43 85 19 86 17 86 51 86 11 87 9 87 59 87 63 88 61 88 7 88 47 89 23 89 21 89 31 90 29 90 39 90 15 91 55 91 53 91 55 92 15 92 13 92 0 93 62 93 1 93 62 94 60 94 63 94 62 95 0 95 4 95 2 96 4 96 0 96 6 97 60 97 62 97 10 98 56 98 58 98 14 99 16 99 52 99 18 100 20 100 48 100 22 101 44 101 46 101 26 102 40 102 42 102 30 103 32 103 36 103 34 104 36 104 32 104 38 105 28 105 30 105 42 106 24 106 26 106 46 107 48 107 20 107 50 108 52 108 16 108 54 109 12 109 14 109 58 110 8 110 10 110 62 111 4 111 6 111 8 112 60 112 6 112 16 113 18 113 50 113 24 114 44 114 22 114 30 115 36 115 38 115 40 116 28 116 38 116 48 117 50 117 18 117 56 118 12 118 54 118 8 119 58 119 60 119 20 120 22 120 46 120 40 121 26 121 28 121 52 122 54 122 14 122 12 123 56 123 10 123</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Camera" name="Camera" type="NODE">
        <matrix sid="transform">0.6747333 -0.3404274 0.6548619 37.45604 0.7378033 0.2876351 -0.6106656 -32.85643 0.01952603 0.8951957 0.4452454 12.72013 0 0 0 1</matrix>
        <instance_camera url="#Camera-camera"/>
      </node>
      <node id="Cylinder_002" name="Cylinder_002" type="NODE">
        <matrix sid="transform">-2.18557e-9 2.18557e-9 20.07999 0 0.05 0 8.77724e-7 0 0 0.05 -8.77724e-7 0 0 0 0 1</matrix>
        <instance_geometry url="#Cylinder_003-mesh" name="Cylinder_002">
          <bind_material>
            <technique_common>
              <instance_material symbol="Metal_001-material" target="#Metal_001-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="Cylinder_009" name="Cylinder_009" type="NODE">
        <matrix sid="transform">-2.18557e-9 2.18557e-9 20.07999 2.876791 0.05 0 8.77724e-7 -3.289865 0 0.05 -8.77724e-7 -16.49283 0 0 0 1</matrix>
        <instance_geometry url="#Cylinder_022-mesh" name="Cylinder_009">
          <bind_material>
            <technique_common>
              <instance_material symbol="Metal_001-material" target="#Metal_001-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>