<?xml version="1.0" ?>
<sdf version="1.5">
  <model name="distance_sensor">
    <pose>0 0 0.035 0 0 0</pose>
    <link name="laser_link">
      <inertial>
        <pose>0.01 0.025 0.025 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <pose>0 0 0 0 1.58 0</pose>
        <geometry>
          <mesh>
            <uri>model://depth_camera_new/meshes/hokuyo.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <sensor name="distance_sensor" type="ray">
        <pose>0 0 0 0 0 0</pose>
        <visualize>false</visualize>
        <update_rate>5</update_rate>
        <ray>
          <scan>
            <horizontal>
              <samples>720</samples>
              <resolution>1</resolution>
              <min_angle>-3.14</min_angle>
              <max_angle>3.14</max_angle>
            </horizontal>
          </scan>
          <range>
            <min>0.40</min>
            <max>30.0</max>
            <resolution>0.01</resolution>
          </range>

        </ray>
        <plugin name="hokuyo_node" filename="libgazebo_ros_laser.so">
          <robotNamespace></robotNamespace>
          <topicName>/scan</topicName>
          <frameName>laser_link</frameName>
        </plugin>
      </sensor>
    </link>
  </model>
</sdf>
