{
   Static initialization of nodelets
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN12class_loader20class_loader_private14registerPluginIN9avoidance19LocalPlannerNodeletEN7nodelet7NodeletEEEvRKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESD_
   fun:ProxyExec0
   fun:__static_initialization_and_destruction_0
   fun:_GLOBAL__sub_I_local_planner_nodelet.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/lib/x86_64-linux-gnu/ld-2.23.so
}

{
   ROS extra thread started for logging 1
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   fun:allocate_dtv
   fun:_dl_allocate_tls
   fun:allocate_stack
   fun:pthread_create@@GLIBC_2.2.5
   fun:_ZN5boost6thread21start_thread_noexceptEv
   fun:_ZN3ros14ROSOutAppenderC1Ev
   fun:_ZN3ros5startEv
   fun:_ZN3ros10NodeHandle9constructERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEb
   fun:_ZN3ros10NodeHandleC1ERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEERKSt3mapIS6_S6_St4lessIS6_ESaISt4pairIS7_S6_EEE
   fun:_ZN9avoidance19LocalPlannerNodeletC1Ev
   fun:_ZN38LocalPlannerNodeletTests_failsafe_Test8TestBodyEv
   fun:_ZN7testing8internal38HandleSehExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
}

{
   ROS extra thread
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   fun:allocate_dtv
   fun:_dl_allocate_tls
   fun:allocate_stack
   fun:pthread_create@@GLIBC_2.2.5
   fun:_ZN5boost6thread21start_thread_noexceptEv
   fun:_ZN5boost6threadC1IRFvvEEEOT_
   fun:_ZN3ros5startEv
   fun:_ZN3ros10NodeHandle9constructERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEb
   fun:_ZN3ros10NodeHandleC1ERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEERKSt3mapIS6_S6_St4lessIS6_ESaISt4pairIS7_S6_EEE
   fun:_ZN9avoidance19LocalPlannerNodeletC1Ev
   fun:_ZN38LocalPlannerNodeletTests_failsafe_Test8TestBodyEv
   fun:_ZN7testing8internal38HandleSehExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
}
