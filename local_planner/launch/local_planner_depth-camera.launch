<launch>

    <arg name="world_file_name"    default="simple_obstacle" />
    <arg name="world_path" default="$(find avoidance)/sim/worlds/$(arg world_file_name).world" />
    <arg name="pointcloud_topics" default="[/iris_obs_avoid/camera/depth/points]"/>


    <!-- Define a static transform from a camera internal frame to the fcu for every camera used -->
    <node pkg="tf" type="static_transform_publisher" name="tf_depth_camera"
          args="0 0 0 -1.57 0 -1.57 fcu camera_link 10"/>

    <!-- Launch PX4 and mavros -->
    <include file="$(find avoidance)/launch/avoidance_sitl_mavros.launch" >
        <arg name="model" value="iris_depth_camera" />
        <arg name="world_path" value="$(arg world_path)" />
    </include>

    <!-- Load custom console configuration -->
    <env name="ROSCONSOLE_CONFIG_FILE" value="$(find local_planner)/resource/custom_rosconsole.conf"/>

    <!-- Launch local planner -->
    <arg name="manager"             default="local_planner_manager"/>

    <node pkg="nodelet" type="nodelet" name="$(arg manager)" args="manager" output="screen"/>
    <node pkg="nodelet" type="nodelet" name="local_planner_nodelet" args="load LocalPlannerNodelet $(arg manager)" output="screen">
      <param name="goal_x_param" value="17" />
      <param name="goal_y_param" value="15"/>
      <param name="goal_z_param" value="3" />
      <param name="world_name" value="$(find avoidance)/sim/worlds/$(arg world_file_name).yaml" />
      <rosparam param="pointcloud_topics" subst_value="True">$(arg pointcloud_topics)</rosparam>
    </node>

    <node name="rviz" pkg="rviz" type="rviz" output="screen" args="-d $(find local_planner)/resource/local_planner.rviz" />

</launch>
