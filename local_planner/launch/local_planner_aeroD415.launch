<launch>
  <arg name="ns" default="/"/>
  <arg name="fcu_url" default="udp://:14540@localhost:14557"/>
  <arg name="gcs_url" default="" />   <!-- GCS link is provided by SITL -->
  <arg name="tgt_system" default="1" />
  <arg name="tgt_component" default="1" />

  <!-- Launch static transform publishers -->
  <node pkg="tf" type="static_transform_publisher" name="tf_depth_camera"
          args="0 0 0 0 0 0 fcu camera_link 10"/>

    <!-- Launch MavROS -->
    <group ns="$(arg ns)">
        <include file="$(find mavros)/launch/node.launch">
            <arg name="pluginlists_yaml" value="$(find mavros)/launch/px4_pluginlists.yaml" />
            <!-- Need to change the config file to get the tf topic and get local position in terms of local origin -->
            <arg name="config_yaml" value="$(find avoidance)/resource/px4_config.yaml" />
            <arg name="fcu_url" value="$(arg fcu_url)" />
            <arg name="gcs_url" value="$(arg gcs_url)" />
            <arg name="tgt_system" value="$(arg tgt_system)" />
            <arg name="tgt_component" value="$(arg tgt_component)" />
        </include>
    </group>

  <!-- Launch Realsense Camera -->
  <include file="$(find realsense2_camera)/launch/rs_rgbd.launch" >
  </include>

  <env name="ROSCONSOLE_CONFIG_FILE" value="$(find local_planner)/resource/custom_rosconsole.conf"/>
  <arg name="pointcloud_topics" default="[/camera/depth_registered/points]"/>

  <!-- Launch Local Planner -->
  <node name="local_planner_node" pkg="local_planner" type="local_planner_node" output="screen" >
    <param name="goal_x_param" value="0" />
    <param name="goal_y_param" value="0"/>
    <param name="goal_z_param" value="4" />
    <rosparam param="pointcloud_topics" subst_value="True">$(arg pointcloud_topics)</rosparam>
  </node>

</launch>
