<!--
Copyright (c) 2018 Intel Corporation

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<launch>
  <arg name="tf_prefix"           default=""/>
  <arg name="namespace"           default="camera"/>
  <arg name="manager"             default="realsense2_camera_manager"/>
  <arg name="required"            default="false"/>

  <!-- Camera device specific arguments -->
  <arg name="serial_no"           default=""/>
  <arg name="json_file_path"      default="$(find local_planner)/resource/stereo_calib.json"/>

  <arg name="enable_depth"      value="true"/>
  <arg name="enable_color"      value="false"/>
  <arg name="enable_infra1"     value="true"/>
  <arg name="enable_infra2"     value="false"/>
  <arg name="enable_pointcloud" value="false"/>
  <arg name="enable_sync"       value="false"/>
  <arg name="align_depth"       value="false"/>

  <arg name="depth_width"       value="640"/>
  <arg name="depth_height"      value="480"/>
  <arg name="depth_fps"         default="15"/>

  <arg name="color_width"       value="640"/>
  <arg name="color_height"      value="480"/>
  <arg name="color_fps"         value="15"/>

  <arg name="infra_fps"         value="15"/>

  <arg name="initial_reset"             default="false"/>

   <group ns="$(arg namespace)">
     <!-- Launch the camera device nodelet-->
     <include file="$(find realsense2_camera)/launch/includes/nodelet.launch.xml">
       <arg name="tf_prefix"                value="$(arg tf_prefix)"/>
       <arg name="manager"                  value="$(arg manager)"/>
       <arg name="serial_no"                value="$(arg serial_no)"/>
       <arg name="json_file_path"           value="$(arg json_file_path)"/>
       <arg name="required"                 value="$(arg required)"/>

       <arg name="enable_pointcloud"        value="$(arg enable_pointcloud)"/>
       <arg name="enable_sync"              value="$(arg enable_sync)"/>
       <arg name="align_depth"              value="$(arg align_depth)"/>
       <arg name="depth_width"              value="$(arg depth_width)"/>
       <arg name="depth_height"             value="$(arg depth_height)"/>
       <arg name="enable_depth"             value="$(arg enable_depth)"/>

       <arg name="color_width"              value="$(arg color_width)"/>
       <arg name="color_height"             value="$(arg color_height)"/>
       <arg name="enable_color"             value="$(arg enable_color)"/>

       <arg name="enable_infra1"            value="$(arg enable_infra1)"/>
       <arg name="enable_infra2"            value="$(arg enable_infra2)"/>

       <arg name="depth_fps"                value="$(arg depth_fps)"/>
       <arg name="color_fps"                value="$(arg color_fps)"/>
       <arg name="infra_fps"                value="$(arg infra_fps)"/>

       <arg name="initial_reset"            value="$(arg initial_reset)"/>
     </include>

      <node pkg="nodelet" type="nodelet" name="points_xyz_sw_registered"
            args="load depth_image_proc/point_cloud_xyz $(arg manager) true" required="$(arg required)">
        <remap from="camera_info"             to="depth/camera_info" />
        <remap from="image_rect" to="depth/image_rect_raw" />
        <remap from="points"     to="depth/points" />
      </node>

</group>

</launch>
