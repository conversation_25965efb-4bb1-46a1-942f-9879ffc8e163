<launch>

  <arg name="world_file_name"    default="simple_obstacle" />
  <arg name="world_path" default="$(find avoidance)/sim/worlds/$(arg world_file_name).world" />
  <arg name="pointcloud_topics" default="[/stereo/points2]"/>

  <!-- Launch PX4 and mavros -->
  <include file="$(find avoidance)/launch/avoidance_sitl_stereo.launch" >
    <arg name="model" value="iris_stereo_camera" />
    <arg name="world_path" value="$(arg world_path)" />
    <arg name="pointcloud_topics" value="$(arg pointcloud_topics)"/>
  </include>


  <!-- Load custom console configuration -->
  <env name="ROSCONSOLE_CONFIG_FILE" value="$(find local_planner)/resource/custom_rosconsole.conf"/>

  <!-- Launch local planner -->
  <node name="local_planner_node" pkg="local_planner" type="local_planner_node" output="screen" >
    <param name="goal_x_param" value="15" />
    <param name="goal_y_param" value="15"/>
    <param name="goal_z_param" value="4" />
    <param name="world_name" value="$(find avoidance)/sim/worlds/$(arg world_file_name).yaml" />
    <rosparam param="pointcloud_topics" subst_value="True">$(arg pointcloud_topics)</rosparam>
  </node>

  <node name="rviz" pkg="rviz" type="rviz" output="screen" args="-d $(find local_planner)/resource/local_planner.rviz" />

  <!-- rosrun topic_tools transform /stereo/disparity /stereo/disparity_image sensor_msgs/Image 'm.image' -->

  <!-- rosrun topic_tools transform /stereoV/disparity /stereoV/disparity_image sensor_msgs/Image 'm.image' -->

  <!--  <node name="transform" pkg="topic_tools" type="transform"
      args="/stereo/disparity /stereo/disparity_image sensor_msgs/Image 'm.image'" />

       <node name="transformV" pkg="topic_tools" type="transform"
      args="/stereoV/disparity /stereoV/disparity_image sensor_msgs/Image 'm.image'" /> -->

</launch>
