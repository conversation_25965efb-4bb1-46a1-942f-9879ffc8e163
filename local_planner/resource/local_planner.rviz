Panels:
  - Class: rviz/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded: ~
      Splitter Ratio: 0.5
    Tree Height: 571
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.588679016
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: Local pointcloud
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /world
      Name: World
      Namespaces:
        "": true
      Queue Size: 100
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /drone
      Name: Drone
      Namespaces:
        "": true
      Queue Size: 100
      Value: true
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.0299999993
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 36
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /path_actual
      Name: path_actual
      Namespaces:
        "": true
      Queue Size: 100
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /path_waypoint
      Name: path_waypoint
      Namespaces:
        "": true
      Queue Size: 100
      Value: true
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /goal_position
      Name: Goal
      Namespaces:
        "": true
      Queue Size: 100
      Value: true
    - Class: rviz/Axes
      Enabled: false
      Length: 1
      Name: fcu
      Radius: 0.100000001
      Reference Frame: fcu
      Value: false
    - Class: rviz/Axes
      Enabled: true
      Length: 1
      Name: local_origin
      Radius: 0.100000001
      Reference Frame: local_origin
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: false
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 20
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: Local pointcloud
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.100000001
      Style: Flat Squares
      Topic: /local_pointcloud
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Class: rviz/Image
      Enabled: true
      Image Topic: /camera/rgb/image_raw
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: Image
      Normalize Range: true
      Queue Size: 2
      Transport Hint: raw
      Unreliable: false
      Value: true
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /complete_tree
      Name: Tree
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /tree_path
      Name: TreePath
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /take_off_pose
      Name: TakeOff
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /original_waypoint
      Name: WpOriginal
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /adapted_waypoint
      Name: WpAdapted
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /smoothed_waypoint
      Name: WpSmoothed
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /current_setpoint
      Name: currenSetpoint
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /closest_point
      Name: Offtrack Closest Point
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /deg60_point
      Name: Offtrack degrees 60 Point
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /fov
      Name: FOV
      Namespaces:
        {}
      Queue Size: 100
      Value: false
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /range_scan
      Name: RangeScan
      Namespaces:
        {}
      Queue Size: 100
      Value: false
  Enabled: true
  Global Options:
    Background Color: 89; 120; 76
    Default Light: true
    Fixed Frame: local_origin
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Class: rviz/ThirdPersonFollower
      Distance: 19.7187691
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.0599999987
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: 0.0648926124
        Y: -0.0846519172
        Z: 0
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.0500000007
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.00999999978
      Pitch: 0.72479701
      Target Frame: fcu
      Value: ThirdPersonFollower (rviz)
      Yaw: 3.09110641
    Saved:
      - Class: rviz/ThirdPersonFollower
        Distance: 10
        Enable Stereo Rendering:
          Stereo Eye Separation: 0.0599999987
          Stereo Focal Distance: 1
          Swap Stereo Eyes: false
          Value: false
        Focal Point:
          X: 0
          Y: 0
          Z: 0
        Focal Shape Fixed Size: true
        Focal Shape Size: 0.0500000007
        Invert Z Axis: false
        Name: ThirdPersonFollower
        Near Clip Distance: 0.00999999978
        Pitch: 0.220398411
        Target Frame: camera_link
        Value: ThirdPersonFollower (rviz)
        Yaw: 4.69858599
      - Class: rviz/Orbit
        Distance: 49.4510117
        Enable Stereo Rendering:
          Stereo Eye Separation: 0.0599999987
          Stereo Focal Distance: 1
          Swap Stereo Eyes: false
          Value: false
        Focal Point:
          X: 5.36913252
          Y: -1.03582704
          Z: -0.00413707877
        Focal Shape Fixed Size: true
        Focal Shape Size: 0.0500000007
        Invert Z Axis: false
        Name: Orbit
        Near Clip Distance: 0.00999999978
        Pitch: 1.56979632
        Target Frame: world
        Value: Orbit (rviz)
        Yaw: 3.12039614
Window Geometry:
  Displays:
    collapsed: false
  Height: 1416
  Hide Left Dock: false
  Hide Right Dock: false
  Image:
    collapsed: false
  QMainWindow State: 000000ff00000000fd00000004000000000000016a000004f2fc020000000afb0000001200530065006c0065006300740069006f006e00000001e10000009b0000006100fffffffb0000001e0054006f006f006c002000500072006f00700065007200740069006500730200000a00000004fd00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c00610079007301000000280000027c000000d700fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb0000000a0049006d00610067006501000002aa000002700000001600fffffffb0000000a0049006d00610067006500000002ce000000c000000000000000000000000100000132000004f2fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a005600690065007700730000000028000004f2000000ad00fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000009bf0000004afc0100000002fb0000000800540069006d00650100000000000009bf0000030000fffffffb0000000800540069006d006501000000000000045000000000000000000000084f000004f200000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 2495
  X: 65
  Y: 24
