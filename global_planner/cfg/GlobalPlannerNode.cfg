#!/usr/bin/env python
PACKAGE = "global_planner"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

# global_planner
gen.add("min_altitude_",    int_t,    0, "Minimum planned altitude", 1,  0, 10)
gen.add("max_altitude_",    int_t,    0, "Maximum planned altitude", 10,  0, 50)
gen.add("max_cell_risk_", double_t, 0, "Maximum risk allowed per cells",    0.2, 0.0,   1.0)
gen.add("smooth_factor_", double_t, 0, "Cost of turning",    20.0, 0.0,   100.0)
gen.add("vert_to_hor_cost_", double_t, 0, "Cost of changing between horizontal and vertical direction",    3.0, 0.0,   10.0)
gen.add("risk_factor_", double_t, 0, "Cost of crashing",    500.0, 0.0,   1000.0)
gen.add("neighbor_risk_flow_", double_t, 0, "The effect of the risk of neighboring cells",    1.0, 0.0,   1.0)
gen.add("explore_penalty_", double_t, 0, "The cost of unexplored space",    0.005, 0.0,   0.01)
gen.add("up_cost_", double_t, 0, "Cost of ascending 1m",    5.0, 0.0,   10.0)
gen.add("down_cost_", double_t, 0, "Cost of descending 1m",    1.0, 0.0,   10.0)
gen.add("search_time_", double_t, 0, "Time it takes to return a new path",    0.5, 0.0,   1.0)
gen.add("min_overestimate_factor_", double_t, 0, "The minimum overestimation for heuristics",    1.03, 1.0,   1.5)
gen.add("max_overestimate_factor_", double_t, 0, "The minimum overestimation for heuristics",    2.0, 1.0,   5.0)
gen.add("risk_threshold_risk_based_speedup_",   double_t,   0, "Risk threshold value for using risk based speedup algorithm",  0.5, 0.0, 1.0)
gen.add("default_speed_",   double_t,   0, "Default speed of flight",  1.0, 0.1, 10.0)
gen.add("max_speed_",   double_t,   0, "Maximum speed of flight",  3.0, 1.0, 10.0)
gen.add("max_iterations_", int_t, 0, "Maximum number of iterations",    2000, 0,   10000)
gen.add("goal_must_be_free_",   bool_t,   0, "Don't bother trying to find a path if the exact goal is occupied",  True)
gen.add("use_current_yaw_",   bool_t,   0, "The current yaw affects the pathfinding",  True)
gen.add("use_risk_heuristics_",   bool_t,   0, "Use non underestimating heuristics for risk",  True)
gen.add("use_speedup_heuristics_",   bool_t,   0, "Use non underestimating heuristics for speedup",  True)
gen.add("use_risk_based_speedup_",   bool_t,   0, "Use risk based speedup",  True)

# global_planner_node
gen.add("clicked_goal_alt_", double_t, 0, "The altitude of clicked goals",    3.5, 0.0,   10.0)
gen.add("clicked_goal_radius_", double_t, 0, "Minimum allowed distance from path end to goal",    1.0, 0.0,   10.0)
gen.add("simplify_iterations_",    int_t,    0, "Maximum number of iterations to simplify a path", 1,  0, 100)
gen.add("simplify_margin_", double_t, 0, "The allowed cost increase for simplifying an edge",    1.01, 0.0,   2.0)

# cell
gen.add("CELL_SCALE", double_t, 2, "Size of a cell, should be divisable by the OctoMap resolution",    1.0, 0.5,   2.0)

# node
gen.add("SPEEDNODE_RADIUS", double_t, 4, "Maximum length of edge between two Cells",    5.0, 0.0,   10.0)

node_type_enum = gen.enum([ gen.const("Node",      			str_t, "Node", 				"Normal node"),
                            gen.const("NodeWithoutSmooth",	str_t, "NodeWithoutSmooth", "No smooth cost"),
                            gen.const("SpeedNode",     		str_t, "SpeedNode", 		"Search with speed")],
                            "Change search mode")

gen.add("default_node_type_", str_t, 4, "Change search mode", "SpeedNode", edit_method=node_type_enum)

exit(gen.generate(PACKAGE, "global_planner", "GlobalPlannerNode"))
