<launch>

    <arg name="world_file_name"    default="simple_obstacle" />
    <arg name="world_path" default="$(find avoidance)/sim/worlds/$(arg world_file_name).world" />
    <arg name="pointcloud_topics" default="[/camera_front/depth/points,/camera_left/depth/points,/camera_right/depth/points]"/>
    <arg name="camera_frame_id" default="camera_link"/>
    <arg name="start_pos_x" default="0.5" />
    <arg name="start_pos_y" default="0.5" />
    <arg name="start_pos_z" default="3.5" />

    <!-- Define a static transform from a camera internal frame to the fcu for every camera used -->
    <node pkg="tf" type="static_transform_publisher" name="tf_camera"
          args="0 0 0 -1.57 0 -1.57 fcu $(arg camera_frame_id) 10"/>
    <node pkg="tf" type="static_transform_publisher" name="tf_front_camera"
          args="0 0 0 -1.57 0 -1.57 fcu front_$(arg camera_frame_id) 10"/>
    <node pkg="tf" type="static_transform_publisher" name="tf_right_camera"
          args="0 0 0 -3.14 0 -1.57 fcu right_$(arg camera_frame_id) 10"/>
    <node pkg="tf" type="static_transform_publisher" name="tf_left_camera"
          args="0 0 0 0 0 -1.57 fcu left_$(arg camera_frame_id) 10"/>

    <!-- Launch PX4 and mavros -->
    <include file="$(find avoidance)/launch/avoidance_sitl_mavros.launch" >
        <arg name="model" value="iris_triple_depth_camera" />
        <arg name="world_path" value="$(arg world_path)" />
    </include>

    <!-- Launch global planner -->
   <include file="$(find global_planner)/launch/global_planner_octomap.launch" >
        <arg name="start_pos_x" value="$(arg start_pos_x)" />
        <arg name="start_pos_y" value="$(arg start_pos_y)" />
        <arg name="start_pos_z" value="$(arg start_pos_z)" />
        <arg name="pointcloud_topics" value="$(arg pointcloud_topics)"/>
        <arg name="camera_frame_id" value="$(arg camera_frame_id)" />
    </include>

    <!-- RViz -->
    <node pkg="rviz" type="rviz" output="screen" name="rviz" respawn="true"
          args="-d $(find global_planner)/resource/global_planner.rviz" />
</launch>
