<launch>
    <arg name="world_file_name"    default="simple_obstacle" />
    <arg name="world_path" default="$(find avoidance)/sim/worlds/$(arg world_file_name).world" />
    <arg name="pointcloud_topics" default="[/stereo/points2]" />
    <arg name="start_pos_x" default="0.5" />
    <arg name="start_pos_y" default="0.5" />
    <arg name="start_pos_z" default="3.5" />

  <!-- Launch PX4 and mavros -->
  <include file="$(find avoidance)/launch/avoidance_sitl_stereo.launch" >
    <arg name="model" value="iris_stereo_camera" />
    <arg name="world_path" value="$(arg world_path)" />
    <arg name="pointcloud_topics" value="$(arg pointcloud_topics)"/>
  </include>

    <!-- Global planner -->
    <include file="$(find global_planner)/launch/global_planner_octomap.launch" >
        <arg name="start_pos_x" value="$(arg start_pos_x)" />
        <arg name="start_pos_y" value="$(arg start_pos_y)" />
        <arg name="start_pos_z" value="$(arg start_pos_z)" />
        <arg name="pointcloud_topics" value="$(arg pointcloud_topics)"/>
    </include>

    <!-- RViz -->
    <node pkg="rviz" type="rviz" output="screen" name="rviz" respawn="true"
          args="-d $(find global_planner)/resource/global_planner.rviz" />
</launch>
