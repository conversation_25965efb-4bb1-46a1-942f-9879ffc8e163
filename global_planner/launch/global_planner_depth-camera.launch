<launch>

    <arg name="world_file_name"    default="simple_obstacle" />
    <arg name="world_path" default="$(find avoidance)/sim/worlds/$(arg world_file_name).world" />
    <arg name="pointcloud_topics" default="[/camera/depth/points]"/>
    <arg name="camera_frame_id" default="camera_link"/>
    <arg name="start_pos_x" default="0.5" />
    <arg name="start_pos_y" default="0.5" />
    <arg name="start_pos_z" default="3.5" />

    <!-- Define a static transform from a camera internal frame to the fcu for every camera used -->
    <!-- Ros transformation -->
    <node pkg="tf" type="static_transform_publisher" name="tf_depth_camera"
          args="0 0 0 -1.57 0 -1.57 fcu $(arg camera_frame_id) 10"/>

    <!-- Launch PX4 and mavros -->
    <include file="$(find avoidance)/launch/avoidance_sitl_mavros.launch" >
        <arg name="model" value="iris_depth_camera" />
        <arg name="world_path" value="$(arg world_path)" />
    </include>


    <!-- Launch local planner -->
    <!-- Global planner -->
    <include file="$(find global_planner)/launch/global_planner_octomap.launch" >
        <arg name="pointcloud_topics" value="$(arg pointcloud_topics)" />
        <arg name="camera_frame_id" value="$(arg camera_frame_id)" />
        <arg name="start_pos_x" value="$(arg start_pos_x)" />
        <arg name="start_pos_y" value="$(arg start_pos_y)" />
        <arg name="start_pos_z" value="$(arg start_pos_z)" />
    </include>>

    <node pkg="rviz" type="rviz" output="screen" name="rviz" respawn="true"
          args="-d $(find global_planner)/resource/global_planner.rviz" />
</launch>
