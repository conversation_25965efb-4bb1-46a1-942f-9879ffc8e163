#!/usr/bin/env python
PACKAGE = "safe_landing_planner"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("n_points_threshold", double_t, 0, "Minimum number of points to be considered in a cell", 100.0,  0.0, 5000.0)
gen.add("std_dev_threshold", double_t, 0, "Threshold on the cell variance to be considered for landing", 0.2,  0.0, 1.0)
gen.add("smoothing_size", int_t, 0, "2*smoothing_size+1 is the smoothing kernel size", 5,  -1, 100)
gen.add("mean_diff_thr", double_t, 0, "Threshold on the mean value difference between two cells", 0.1, 0.0, 1.0)
gen.add("max_n_mean_diff_cells", int_t, 0, "Maxmum number of cells in the neighborhood that can be different more than mean_diff_thr", 70, 0, 100)
gen.add("min_n_land_cells", int_t, 0, "Minimum cell number that need to be flat in the neighborhood", 70,  0, 100)

gen.add("grid_size", double_t, 0, "Size of the square grid in meters ", 10.0,  1.0, 20.0)
gen.add("cell_size", double_t, 0, "Size of the square cells in the grid in meters ", 0.25,  0.1, 10.0)
gen.add("alpha", double_t, 0, "History parameter on mean/variance temporal smoothing", 0.9,  0.0, 1.0)

gen.add("timeout_critical", double_t, 0, "After this timeout the companion status is MAV_STATE_CRITICAL", 0.5, 0, 10)
gen.add("timeout_termination", double_t, 0, "After this timeout the companion status is MAV_STATE_FLIGHT_TERMINATION", 15, 0, 1000)


exit(gen.generate(PACKAGE, "safe_landing_planner", "SafeLandingPlannerNode"))
