#!/usr/bin/env python
PACKAGE = "safe_landing_planner"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("can_land_thr", double_t, 0, "Hysteresis on landable cell", 0.4, 0.0, 1.0)
gen.add("loiter_height", double_t, 0, "Z distance to the pointcloud to start loitering ", 8.0, 1, 12)
gen.add("smoothing_land_cell", int_t, 0, "Kernel size on cell land hysteresis", 6,  0, 100)
gen.add("beta", double_t, 0, "History paramter on land decision per cell", 0.9, 0.0, 1.0)
gen.add("vertical_range_error", double_t, 0, "If the different to loiter_height is greater than this paramter, the vehicle adjust altitude before taking decision", 0.5, 0.0, 4.0)
gen.add("spiral_width", double_t, 0, "Factor to increase squared spiral width", 2.0, 1.0, 10.0)


exit(gen.generate(PACKAGE, "safe_landing_planner", "WaypointGeneratorNode"))
