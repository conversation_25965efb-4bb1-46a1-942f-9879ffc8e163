<launch>

    <arg name="world_file_name"    default="simple_obstacle" />
    <arg name="world_path" default="$(find avoidance)/sim/worlds/$(arg world_file_name).world" />
    <arg name="pointcloud_topics" default="/camera/depth/points"/>

    <!-- Define a static transform from a camera internal frame to the fcu for every camera used -->
    <node pkg="tf" type="static_transform_publisher" name="tf_depth_camera"
          args="0 0 0 1.57 3.14 0 fcu camera_link 10"/>

    <!-- Launch PX4 and mavros -->
    <include file="$(find avoidance)/launch/avoidance_sitl_mavros.launch" >
        <arg name="model" value="iris_downward_depth_camera" />
        <arg name="vehicle" value="iris" />
        <arg name="world_path" value="$(arg world_path)" />
    </include>

    <!-- Launch local planner -->
    <node name="safe_landing_planner_node" pkg="safe_landing_planner" type="safe_landing_planner_node" output="screen">
      <param name="pointcloud_topics" value="$(arg pointcloud_topics)" />
      <param name="world_name" value="$(find avoidance)/sim/worlds/$(arg world_file_name).yaml" />
    </node>

    <node name="waypoint_generator_node" pkg="safe_landing_planner" type="waypoint_generator_node" output="screen" >
    </node>

    <node name="dynparam_slpn" pkg="dynamic_reconfigure" type="dynparam" args="load safe_landing_planner_node $(find safe_landing_planner)/cfg/slpn.yaml" />
    <node name="dynparam_wpgn" pkg="dynamic_reconfigure" type="dynparam" args="load waypoint_generator_node $(find safe_landing_planner)/cfg/wpgn.yaml" />

    <node name="rviz" pkg="rviz" type="rviz" output="screen" args="-d $(find safe_landing_planner)/resource/safe_landing_planner.rviz" />

</launch>
