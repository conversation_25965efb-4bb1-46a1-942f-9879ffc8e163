<launch>
    <arg name="pointcloud_topics" default="/camera/depth/points"/>
    <arg name="bag_name" default="/not_specified_bag_name" />
    <param name="use_sim_time" value="true" />

    <!-- Launch rqt_reconfigure -->
    <node pkg="rqt_reconfigure" type="rqt_reconfigure" output="screen" name="rqt_reconfigure" />

    <node pkg="rosbag" type="play" name="player" required="true" output="screen" args="--clock $(arg bag_name)">
       <remap from="/grid" to="/rosbag/grid"/>
       <remap from="/grid_slp" to="/rosbag/grid_slp"/>  <!-- Grid from the bag to compare decision -->
       <remap from="/grid_slp" to="/raw_grid_slp"/> <!-- Grid from the bag as input to the replay -->
       <remap from="/land_hysteresis" to="/rosbag/land_hysteresis"/>
       <remap from="/path_actual" to="/rosbag/path_actual"/>
       <remap from="/mavros/trajectory/generated" to="/rosbag/mavros/trajectory/generated"/>

    </node>

    <node name="safe_landing_planner_node" pkg="safe_landing_planner" type="safe_landing_planner_node" output="screen">
      <param name="pointcloud_topics" value="$(arg pointcloud_topics)" />
      <param name="play_rosbag" value="true" />
    </node>

    <node name="waypoint_generator_node" pkg="safe_landing_planner" type="waypoint_generator_node" output="screen" >
    </node>

    <node name="rviz" pkg="rviz" type="rviz" output="screen" args="-d $(find safe_landing_planner)/resource/safe_landing_planner_rosbag.rviz" />

</launch>
