{"python.autoComplete.extraPaths": ["/home/<USER>/catkin_ws/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "python.analysis.extraPaths": ["/home/<USER>/catkin_ws/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "files.associations": {"atomic": "cpp", "mutex": "cpp", "thread": "cpp", "stdexcept": "cpp", "cmath": "cpp", "string": "cpp", "condition_variable": "cpp", "numeric": "cpp"}, "cmake.sourceDirectory": "/home/<USER>/catkin_ws/src/avoidance/avoidance"}