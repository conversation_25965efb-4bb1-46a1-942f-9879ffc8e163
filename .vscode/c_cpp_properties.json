{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/catkin_ws/devel/include/**", "/opt/ros/noetic/include/**", "/home/<USER>/catkin_ws/src/avoidance/avoidance/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/fast_planner/bspline/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/fast_planner/bspline_opt/include/**", "/home/<USER>/catkin_ws/src/mavros_controllers/controller_msgs/include/**", "/home/<USER>/catkin_ws/src/offboard_py/include/**", "/home/<USER>/catkin_ws/src/geographic_info/geodesy/include/**", "/home/<USER>/catkin_ws/src/mavros_controllers/geometric_controller/include/**", "/home/<USER>/catkin_ws/src/avoidance/global_planner/include/**", "/home/<USER>/catkin_ws/src/mavros/libmavconn/include/**", "/home/<USER>/catkin_ws/src/avoidance/local_planner/include/**", "/home/<USER>/catkin_ws/src/mavros/mavros/include/**", "/home/<USER>/catkin_ws/src/mavros/mavros_msgs/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/uav_simulator/Utils/multi_map_server/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/fast_planner/path_searching/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/fast_planner/plan_env/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/fast_planner/plan_manage/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/fast_planner/poly_traj/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/uav_simulator/Utils/pose_utils/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/uav_simulator/Utils/quadrotor_msgs/include/**", "/home/<USER>/catkin_ws/src/avoidance/safe_landing_planner/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/uav_simulator/so3_control/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/uav_simulator/so3_disturbance_generator/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/uav_simulator/so3_quadrotor_simulator/include/**", "/home/<USER>/catkin_ws/src/mavros/test_mavros/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/fast_planner/traj_utils/include/**", "/home/<USER>/catkin_ws/src/mavros_controllers/trajectory_publisher/include/**", "/home/<USER>/catkin_ws/src/Fast-Planner/uav_simulator/Utils/uav_utils/include/**", "/home/<USER>/catkin_ws/src/unique_identifier/unique_id/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}